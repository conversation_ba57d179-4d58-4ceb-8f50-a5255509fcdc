# OTA订单处理系统 - 功能实现指南

## 实现概述

本系统采用纯前端架构，集成了多个AI服务和智能选择功能，实现了从订单输入到自动创建的完整流程。

## 核心功能实现

### 1. Google Vision API 图像分析

#### 1.1 ImageService类实现
```javascript
class ImageService {
    /**
     * @function extractTextFromImage - 从图像中提取文字
     * @param {File} file - 图像文件
     * @returns {Promise<string>} 提取的文字内容
     */
    async extractTextFromImage(file) {
        const base64Data = await this.fileToBase64(file);
        const response = await this.callGoogleVisionAPI(base64Data, ['TEXT_DETECTION']);
        return this.parseVisionResponse(response);
    }

    /**
     * @function analyzeImageContent - 完整图像分析
     * @param {File} file - 图像文件
     * @returns {Promise<Object>} 分析结果包含文字、标签、物体
     */
    async analyzeImageContent(file) {
        const base64Data = await this.fileToBase64(file);
        const features = ['TEXT_DETECTION', 'DOCUMENT_TEXT_DETECTION', 'LABEL_DETECTION', 'OBJECT_LOCALIZATION'];
        const response = await this.callGoogleVisionAPI(base64Data, features);
        
        return {
            extractedText: this.parseVisionResponse(response),
            labels: this.parseLabels(response),
            objects: this.parseObjects(response),
            confidence: this.calculateConfidence(response)
        };
    }
}
```

#### 1.2 API请求结构
```javascript
// Google Vision API请求格式
const visionRequest = {
    requests: [{
        image: { content: base64ImageData },
        features: [
            { type: 'TEXT_DETECTION', maxResults: 10 },
            { type: 'DOCUMENT_TEXT_DETECTION', maxResults: 10 },
            { type: 'LABEL_DETECTION', maxResults: 10 },
            { type: 'OBJECT_LOCALIZATION', maxResults: 10 }
        ]
    }]
};
```

### 2. 双LLM架构实现

#### 2.1 LLMService类
```javascript
class LLMService {
    constructor() {
        this.deepseekStatus = { connectionStatus: 'checking' };
        this.geminiStatus = { connectionStatus: 'checking' };
        this.currentLLM = 'deepseek'; // 默认使用DeepSeek
    }

    /**
     * @function processOrderWithAI - 使用AI处理订单
     * @param {string} orderContent - 订单内容
     * @param {string} otaType - OTA类型
     * @returns {Promise<Object>} 处理结果
     */
    async processOrderWithAI(orderContent, otaType) {
        const selectedLLM = this.selectBestLLM();
        
        try {
            if (selectedLLM === 'deepseek') {
                return await this.processWithDeepSeek(orderContent, otaType);
            } else {
                return await this.processWithGemini(orderContent, otaType);
            }
        } catch (error) {
            // 自动切换到后备LLM
            const fallbackLLM = selectedLLM === 'deepseek' ? 'gemini' : 'deepseek';
            logger.logError(`${selectedLLM} 处理失败，切换到 ${fallbackLLM}`, error);
            return await this.processWithFallback(fallbackLLM, orderContent, otaType);
        }
    }

    /**
     * @function selectBestLLM - 选择最佳LLM
     * @returns {string} 选择的LLM名称
     */
    selectBestLLM() {
        // 优先使用DeepSeek
        if (this.deepseekStatus.connectionStatus === 'connected') {
            return 'deepseek';
        }
        
        // DeepSeek不可用时使用Gemini
        if (this.geminiStatus.connectionStatus === 'connected') {
            return 'gemini';
        }
        
        // 都不可用时默认尝试DeepSeek
        return 'deepseek';
    }
}
```

#### 2.2 DeepSeek API集成
```javascript
/**
 * @function processWithDeepSeek - 使用DeepSeek处理订单
 * @param {string} content - 订单内容
 * @param {string} otaType - OTA类型
 * @returns {Promise<Object>} 处理结果
 */
async processWithDeepSeek(content, otaType) {
    const prompt = this.generatePrompt(content, otaType);
    
    const requestBody = {
        model: CONFIG.DEEPSEEK.MODEL_CONFIG.model,
        messages: [
            { role: "system", content: "你是一个专业的OTA订单处理助手..." },
            { role: "user", content: prompt }
        ],
        temperature: CONFIG.DEEPSEEK.MODEL_CONFIG.temperature,
        max_tokens: CONFIG.DEEPSEEK.MODEL_CONFIG.max_tokens,
        top_p: CONFIG.DEEPSEEK.MODEL_CONFIG.top_p
    };

    const response = await fetch(CONFIG.DEEPSEEK.API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${CONFIG.DEEPSEEK.API_KEY}`
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(CONFIG.DEEPSEEK.TIMEOUT)
    });

    const data = await response.json();
    return this.parseDeepSeekResponse(data);
}
```

#### 2.3 Gemini API集成
```javascript
/**
 * @function processWithGemini - 使用Gemini处理订单
 * @param {string} content - 订单内容
 * @param {string} otaType - OTA类型
 * @returns {Promise<Object>} 处理结果
 */
async processWithGemini(content, otaType) {
    const prompt = this.generatePrompt(content, otaType);
    
    const requestBody = {
        contents: [{
            parts: [{ text: prompt }]
        }],
        generationConfig: CONFIG.GEMINI.MODEL_CONFIG
    };

    const response = await fetch(`${CONFIG.GEMINI.API_URL}?key=${CONFIG.GEMINI.API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(CONFIG.GEMINI.TIMEOUT)
    });

    const data = await response.json();
    return this.parseGeminiResponse(data);
}
```

### 3. 智能选择功能实现

#### 3.1 SmartSelectionService类
```javascript
class SmartSelectionService {
    constructor(apiService) {
        this.apiService = apiService;
    }

    /**
     * @function selectBackendUser - 智能选择后台用户
     * @param {Object} order - 订单数据
     * @returns {Object} 选择结果
     */
    selectBackendUser(order) {
        const users = this.apiService.getBackendUsers();
        
        // 优先级1: 指定用户jcy1 (ID: 338)
        const jcy1User = users.find(user => user.id === 338 && user.name === 'jcy1');
        if (jcy1User) {
            return {
                selectedUserId: jcy1User.id,
                selectedUserName: jcy1User.name,
                selectedUserRole: jcy1User.role,
                selectionReason: "使用指定的默认用户 jcy1 (ID: 338)"
            };
        }

        // 优先级2: Chong Dealer类型用户
        if (order.otaType === 'chong_dealer') {
            const chongUser = users.find(user => 
                user.name && (user.name.toLowerCase().includes('chong') || user.name.toLowerCase().includes('jcy'))
            );
            if (chongUser) {
                return {
                    selectedUserId: chongUser.id,
                    selectedUserName: chongUser.name,
                    selectedUserRole: chongUser.role,
                    selectionReason: `Chong Dealer类型选择用户: ${chongUser.name}`
                };
            }
        }

        // 优先级3: Sub_Admin角色用户
        const subAdminUser = users.find(user => user.role === 'Sub_Admin');
        if (subAdminUser) {
            return {
                selectedUserId: subAdminUser.id,
                selectedUserName: subAdminUser.name,
                selectedUserRole: subAdminUser.role,
                selectionReason: "选择Sub_Admin角色用户"
            };
        }

        // 优先级4: Operator角色用户
        const operatorUser = users.find(user => user.role === 'Operator');
        if (operatorUser) {
            return {
                selectedUserId: operatorUser.id,
                selectedUserName: operatorUser.name,
                selectedUserRole: operatorUser.role,
                selectionReason: "选择Operator角色用户"
            };
        }

        // 默认选择第一个用户
        const defaultUser = users[0];
        return {
            selectedUserId: defaultUser.id,
            selectedUserName: defaultUser.name,
            selectedUserRole: defaultUser.role,
            selectionReason: "使用默认用户（第一个可用用户）"
        };
    }

    /**
     * @function selectCarType - 智能选择车型
     * @param {Object} order - 订单数据
     * @returns {Object} 选择结果
     */
    selectCarType(order) {
        const carTypes = this.apiService.getCarTypes();
        const passengerCount = parseInt(order.passenger_number) || 0;

        // 根据人数选择车型
        let selectedCarType;
        let selectionReason;

        if (passengerCount <= 2) {
            selectedCarType = carTypes.find(car => car.id === 5); // Compact 5 Seater
            selectionReason = `${passengerCount}人选择紧凑型5座车`;
        } else if (passengerCount <= 4) {
            selectedCarType = carTypes.find(car => car.id === 6); // Comfort 5 Seater
            selectionReason = `${passengerCount}人选择舒适型5座车`;
        } else if (passengerCount <= 6) {
            selectedCarType = carTypes.find(car => car.id === 16); // Standard Size MPV
            selectionReason = `${passengerCount}人选择标准MPV`;
        } else if (passengerCount <= 8) {
            selectedCarType = carTypes.find(car => car.id === 20); // 10 Seater MPV/Van
            selectionReason = `${passengerCount}人选择10座MPV`;
        } else if (passengerCount <= 11) {
            selectedCarType = carTypes.find(car => car.id === 30); // 12 Seater MPV
            selectionReason = `${passengerCount}人选择12座MPV`;
        } else {
            // 超过11人选择最大的车型
            selectedCarType = carTypes.find(car => car.seat_number >= 12) || carTypes[carTypes.length - 1];
            selectionReason = `${passengerCount}人选择大型车辆`;
        }

        // 如果没有指定人数，使用默认车型
        if (!selectedCarType) {
            selectedCarType = carTypes.find(car => car.id === 6); // 默认Comfort 5 Seater
            selectionReason = "未指定人数，使用默认车型 Comfort 5 Seater";
        }

        return {
            selectedCarTypeId: selectedCarType.id,
            selectedCarTypeName: selectedCarType.type,
            selectedSeatNumber: selectedCarType.seat_number,
            selectionReason: selectionReason
        };
    }

    /**
     * @function selectSubCategory - 智能选择子分类
     * @param {Object} order - 订单数据
     * @returns {Object} 选择结果
     */
    selectSubCategory(order) {
        const subCategories = this.apiService.getSubCategories();
        const serviceType = this.detectServiceType(order);

        let selectedCategory;
        let selectionReason;

        switch (serviceType) {
            case 'pickup':
                selectedCategory = subCategories.find(cat => cat.id === 7); // Pickup
                selectionReason = "检测到接机服务";
                break;
            case 'dropoff':
                selectedCategory = subCategories.find(cat => cat.id === 8); // Dropoff
                selectionReason = "检测到送机服务";
                break;
            case 'ctrip':
                selectedCategory = subCategories.find(cat => cat.id === 15); // 携程1
                selectionReason = "检测到携程订单";
                break;
            case 'genting':
                selectedCategory = subCategories.find(cat => cat.id === 9); // KL to genting
                selectionReason = "检测到云顶包车服务";
                break;
            case 'melaka':
                selectedCategory = subCategories.find(cat => cat.id === 10); // KL to melaka
                selectionReason = "检测到马六甲包车服务";
                break;
            case 'sekinchan':
                selectedCategory = subCategories.find(cat => cat.id === 36); // Sekinchan
                selectionReason = "检测到适耕庄包车服务";
                break;
            case 'charter':
                selectedCategory = subCategories.find(cat => cat.id === 43); // Charter
                selectionReason = "检测到通用包车服务";
                break;
            default:
                selectedCategory = subCategories.find(cat => cat.id === 7); // 默认Pickup
                selectionReason = "未能确定服务类型，使用默认接机服务";
        }

        return {
            selectedSubCategoryId: selectedCategory.id,
            selectedSubCategoryName: selectedCategory.name,
            selectedMainCategory: selectedCategory.main_category,
            selectionReason: selectionReason
        };
    }

    /**
     * @function detectServiceType - 检测服务类型
     * @param {Object} order - 订单数据
     * @returns {string} 服务类型
     */
    detectServiceType(order) {
        const content = (order.notes || '').toLowerCase();
        const pickup = (order.pickup || '').toLowerCase();
        const destination = (order.destination || '').toLowerCase();

        // 检测携程订单
        if (content.includes('携程') || content.includes('ctrip')) {
            return 'ctrip';
        }

        // 检测包车服务
        if (content.includes('云顶') || content.includes('genting')) {
            return 'genting';
        }
        if (content.includes('马六甲') || content.includes('melaka')) {
            return 'melaka';
        }
        if (content.includes('适耕庄') || content.includes('sekinchan')) {
            return 'sekinchan';
        }
        if (content.includes('包车') || content.includes('charter')) {
            return 'charter';
        }

        // 检测接送机服务
        if (pickup.includes('klia') || pickup.includes('机场') || pickup.includes('airport')) {
            return 'pickup'; // 接机
        }
        if (destination.includes('klia') || destination.includes('机场') || destination.includes('airport')) {
            return 'dropoff'; // 送机
        }

        // 默认为接机服务
        return 'pickup';
    }

    /**
     * @function getSelectionSummary - 获取完整选择摘要
     * @param {Object} order - 订单数据
     * @returns {Object} 完整选择结果
     */
    getSelectionSummary(order) {
        const userSelection = this.selectBackendUser(order);
        const carTypeSelection = this.selectCarType(order);
        const categorySelection = this.selectSubCategory(order);

        return {
            backendUserId: userSelection.selectedUserId,
            backendUserName: userSelection.selectedUserName,
            backendUserRole: userSelection.selectedUserRole,
            carTypeId: carTypeSelection.selectedCarTypeId,
            carTypeName: carTypeSelection.selectedCarTypeName,
            subCategoryId: categorySelection.selectedSubCategoryId,
            subCategoryName: categorySelection.selectedSubCategoryName,
            selectionReasons: {
                user: userSelection.selectionReason,
                carType: carTypeSelection.selectionReason,
                category: categorySelection.selectionReason
            }
        };
    }
}
```

### 4. 订单处理器实现

#### 4.1 OrderProcessor类
```javascript
class OrderProcessor {
    constructor(apiService, smartSelectionService) {
        this.apiService = apiService;
        this.smartSelectionService = smartSelectionService;
    }

    /**
     * @function buildOrderData - 构建订单数据
     * @param {Object} order - 原始订单数据
     * @returns {Object} 构建的订单数据
     */
    buildOrderData(order) {
        // 获取智能选择结果
        const smartSelection = this.smartSelectionService.getSelectionSummary(order);

        // 记录智能选择日志
        logger.logInfo('[订单构建] 智能选择结果应用');
        logger.logInfo(`- selectedBackendUser: "${smartSelection.backendUserName} (ID: ${smartSelection.backendUserId})"`);
        logger.logInfo(`- selectedSubCategory: "${smartSelection.subCategoryName} (ID: ${smartSelection.subCategoryId})"`);
        logger.logInfo(`- selectedCarType: "${smartSelection.carTypeName} (ID: ${smartSelection.carTypeId})"`);

        // 构建API请求数据
        const data = {
            sub_category_id: smartSelection.subCategoryId,
            ota_reference_number: order.ota_reference_number,
            car_type_id: smartSelection.carTypeId,
            incharge_by_backend_user_id: smartSelection.backendUserId,
            customer_name: order.customer_name,
            customer_contact: order.customer_contact,
            flight_info: order.flight_info,
            pickup: order.pickup,
            destination: order.destination,
            date: order.date,
            time: order.time,
            passenger_number: order.passenger_number,
            notes: order.notes
        };

        // 移除空值字段
        Object.keys(data).forEach(key => {
            if (data[key] === null || data[key] === undefined || data[key] === '') {
                delete data[key];
            }
        });

        return data;
    }

    /**
     * @function generateReference - 生成OTA参考号
     * @param {Object} order - 订单数据
     * @returns {string} 生成的参考号
     */
    generateReference(order) {
        const now = new Date();
        const dateStr = now.getFullYear().toString() +
                       (now.getMonth() + 1).toString().padStart(2, '0') +
                       now.getDate().toString().padStart(2, '0') +
                       now.getHours().toString().padStart(2, '0') +
                       now.getMinutes().toString().padStart(2, '0');

        const flightNumber = order.flight_number || 'NOFLIGHT';
        const customerName = order.customer_name || 'NONAME';

        return `${dateStr}-${flightNumber}-${customerName}`;
    }

    /**
     * @function validateOrderData - 验证订单数据
     * @param {Object} order - 订单数据
     * @returns {Object} 验证结果
     */
    validateOrderData(order) {
        const errors = [];
        const warnings = [];

        // 必填字段检查
        if (!order.customer_name) errors.push('客户姓名不能为空');
        if (!order.date) errors.push('日期不能为空');
        if (!order.time) errors.push('时间不能为空');

        // 数据格式检查
        if (order.date && !/^\d{4}-\d{2}-\d{2}$/.test(order.date)) {
            errors.push('日期格式不正确，应为YYYY-MM-DD');
        }
        if (order.time && !/^\d{2}:\d{2}$/.test(order.time)) {
            errors.push('时间格式不正确，应为HH:MM');
        }

        // 业务逻辑检查
        if (order.passenger_number && order.passenger_number > 50) {
            warnings.push('乘客数量过多，请确认');
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }
}
```

### 5. 状态指示器实现

#### 5.1 双LLM状态指示器
```javascript
/**
 * @function updateGeminiStatus - 更新Gemini状态指示器
 * @param {string} status - 连接状态
 * @param {Object} details - 状态详情
 */
function updateGeminiStatus(status, details = {}) {
    const indicator = document.getElementById('gemini-status');
    const statusText = document.getElementById('gemini-status-text');

    // 移除所有状态类
    indicator.classList.remove('connected', 'disconnected', 'checking');

    switch (status) {
        case 'connected':
            indicator.classList.add('connected');
            statusText.textContent = 'Gemini: 已连接 (后备)';
            break;
        case 'disconnected':
            indicator.classList.add('disconnected');
            statusText.textContent = 'Gemini: 连接失败';
            break;
        case 'checking':
            indicator.classList.add('checking');
            statusText.textContent = 'Gemini: 检测中...';
            break;
    }
}

/**
 * @function updateDeepSeekStatus - 更新DeepSeek状态指示器
 * @param {string} status - 连接状态
 * @param {Object} details - 状态详情
 */
function updateDeepSeekStatus(status, details = {}) {
    const indicator = document.getElementById('deepseek-status');
    const statusText = document.getElementById('deepseek-status-text');

    // 移除所有状态类
    indicator.classList.remove('connected', 'disconnected', 'checking');

    switch (status) {
        case 'connected':
            indicator.classList.add('connected');
            statusText.textContent = 'DeepSeek: 已连接 (主要)';
            break;
        case 'disconnected':
            indicator.classList.add('disconnected');
            statusText.textContent = 'DeepSeek: 连接失败';
            break;
        case 'checking':
            indicator.classList.add('checking');
            statusText.textContent = 'DeepSeek: 检测中...';
            break;
    }
}
```
