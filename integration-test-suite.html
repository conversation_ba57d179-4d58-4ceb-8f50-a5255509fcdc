<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统集成测试套件</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        
        .test-section.full-width {
            grid-column: 1 / -1;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            width: 24px;
            height: 24px;
            display: inline-block;
        }
        
        .results {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        
        .results:empty::before {
            content: '等待测试结果...';
            color: #6c757d;
            font-style: italic;
        }
        
        button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        button.success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }
        
        button.danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        
        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            border: 2px dashed #007bff;
            border-radius: 6px;
            background-color: #f8f9fa;
            width: 100%;
            transition: border-color 0.3s ease;
        }
        
        input[type="file"]:hover {
            border-color: #0056b3;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .summary {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }
        
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 OTA订单处理系统 - 集成测试套件</h1>
        <p>Google Vision API + DeepSeek/Gemini LLM 完整集成测试</p>
    </div>

    <div class="test-container">
        <!-- 配置验证测试 -->
        <div class="test-section">
            <h2>
                <span class="icon">⚙️</span>
                配置验证测试
            </h2>
            <p>验证所有API密钥和配置是否正确设置</p>
            <button onclick="runConfigurationTests()">运行配置测试</button>
            <div id="configResults" class="results"></div>
        </div>

        <!-- Google Vision API测试 -->
        <div class="test-section">
            <h2>
                <span class="icon">👁️</span>
                Google Vision API测试
            </h2>
            <p>测试图片文字提取和内容分析功能</p>
            <input type="file" id="visionTestImage" accept="image/*" title="选择测试图片">
            <button onclick="runVisionAPITests()">测试Vision API</button>
            <div id="visionResults" class="results"></div>
        </div>

        <!-- LLM服务测试 -->
        <div class="test-section">
            <h2>
                <span class="icon">🤖</span>
                LLM服务测试
            </h2>
            <p>测试DeepSeek和Gemini LLM的连接和切换逻辑</p>
            <button onclick="runLLMTests()">测试LLM服务</button>
            <div id="llmResults" class="results"></div>
        </div>

        <!-- 端到端工作流测试 -->
        <div class="test-section">
            <h2>
                <span class="icon">🔄</span>
                端到端工作流测试
            </h2>
            <p>测试从图片上传到订单处理的完整流程</p>
            <button onclick="runWorkflowTests()">测试完整工作流</button>
            <div id="workflowResults" class="results"></div>
        </div>

        <!-- 集成测试结果 -->
        <div class="test-section full-width">
            <h2>
                <span class="icon">📊</span>
                集成测试结果
            </h2>
            <p>运行所有测试并显示综合结果</p>
            <div style="margin-bottom: 15px;">
                <button onclick="runAllIntegrationTests()" class="success">运行所有测试</button>
                <button onclick="clearAllResults()" class="danger">清除结果</button>
            </div>
            <div class="progress-bar">
                <div id="testProgress" class="progress-fill"></div>
            </div>
            <div id="integrationResults" class="results"></div>
        </div>
    </div>

    <!-- 测试摘要 -->
    <div class="summary">
        <h2>📋 测试摘要</h2>
        <div id="testSummary">
            <p>点击"运行所有测试"按钮开始完整的集成测试。</p>
            <p><strong>测试覆盖范围：</strong></p>
            <ul>
                <li>✅ API配置验证（Google Vision, DeepSeek, Gemini）</li>
                <li>✅ 图片处理工作流</li>
                <li>✅ 文字提取和图像分析</li>
                <li>✅ LLM服务选择和备份机制</li>
                <li>✅ 端到端订单处理流程</li>
            </ul>
        </div>
    </div>

    <!-- 引入所有必要的脚本 -->
    <script src="config.js"></script>
    <script src="logger.js"></script>
    <script src="prompts-config.js"></script>
    <script src="app.js"></script>
    <script src="integration-test.js"></script>

    <script>
        let integrationTester;
        let testProgress = 0;

        // 初始化测试器
        function initializeTester() {
            if (typeof IntegrationTester !== 'undefined') {
                integrationTester = new IntegrationTester();
                return true;
            }
            return false;
        }

        // 配置验证测试
        async function runConfigurationTests() {
            const resultsDiv = document.getElementById('configResults');
            resultsDiv.innerHTML = '<div class="info">正在运行配置测试...</div>';
            
            if (!initializeTester()) {
                resultsDiv.innerHTML = '<div class="error">测试器初始化失败</div>';
                return;
            }

            try {
                const visionConfigOK = integrationTester.testVisionAPIConfiguration();
                const llmConfigOK = integrationTester.testLLMConfiguration();
                
                let results = [];
                results.push(visionConfigOK ? 
                    '<span class="success">✓ Google Vision API 配置正确</span>' : 
                    '<span class="error">✗ Google Vision API 配置错误</span>');
                results.push(llmConfigOK ? 
                    '<span class="success">✓ LLM 配置正确</span>' : 
                    '<span class="error">✗ LLM 配置错误</span>');
                
                resultsDiv.innerHTML = results.join('<br>');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">配置测试失败: ${error.message}</div>`;
            }
        }

        // Google Vision API测试
        async function runVisionAPITests() {
            const resultsDiv = document.getElementById('visionResults');
            const fileInput = document.getElementById('visionTestImage');
            
            if (!fileInput.files.length) {
                resultsDiv.innerHTML = '<div class="warning">请先选择一张测试图片</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="info">正在测试 Google Vision API...</div>';
            
            if (!initializeTester()) {
                resultsDiv.innerHTML = '<div class="error">测试器初始化失败</div>';
                return;
            }

            try {
                const file = fileInput.files[0];
                
                // 测试图片处理工作流
                const workflowOK = await integrationTester.testImageProcessingWorkflow();
                
                // 测试API连接性
                const connectivityOK = await integrationTester.testVisionAPIConnectivity();
                
                let results = [];
                results.push(workflowOK ? 
                    '<span class="success">✓ 图片处理工作流正常</span>' : 
                    '<span class="error">✗ 图片处理工作流异常</span>');
                results.push(connectivityOK ? 
                    '<span class="success">✓ API连接配置正确</span>' : 
                    '<span class="error">✗ API连接配置错误</span>');
                
                results.push('<div class="info">💡 提示: 要测试真实API调用，请使用 test-google-vision.html</div>');
                
                resultsDiv.innerHTML = results.join('<br>');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Vision API测试失败: ${error.message}</div>`;
            }
        }

        // LLM服务测试
        async function runLLMTests() {
            const resultsDiv = document.getElementById('llmResults');
            resultsDiv.innerHTML = '<div class="info">正在测试 LLM 服务...</div>';
            
            if (!initializeTester()) {
                resultsDiv.innerHTML = '<div class="error">测试器初始化失败</div>';
                return;
            }

            try {
                const llmConfigOK = integrationTester.testLLMConfiguration();
                
                let results = [];
                results.push(llmConfigOK ? 
                    '<span class="success">✓ LLM 配置和优先级正确</span>' : 
                    '<span class="error">✗ LLM 配置或优先级错误</span>');
                
                // 检查DeepSeek优先级
                if (integrationTester.llmService) {
                    const selectedLLM = integrationTester.llmService.selectBestLLM();
                    results.push(`<span class="info">当前选择的LLM: ${selectedLLM.name}</span>`);
                    results.push(`<span class="info">备用LLM策略: ${selectedLLM.name === 'DeepSeek' ? 'Gemini' : 'DeepSeek'}</span>`);
                }
                
                resultsDiv.innerHTML = results.join('<br>');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">LLM测试失败: ${error.message}</div>`;
            }
        }

        // 端到端工作流测试
        async function runWorkflowTests() {
            const resultsDiv = document.getElementById('workflowResults');
            resultsDiv.innerHTML = '<div class="info">正在测试端到端工作流...</div>';
            
            if (!initializeTester()) {
                resultsDiv.innerHTML = '<div class="error">测试器初始化失败</div>';
                return;
            }

            try {
                const workflowOK = await integrationTester.testCompleteIntegration();
                
                let results = [];
                results.push(workflowOK ? 
                    '<span class="success">✓ 端到端工作流测试通过</span>' : 
                    '<span class="error">✗ 端到端工作流测试失败</span>');
                
                results.push('<div class="info">工作流程:</div>');
                results.push('<div style="margin-left: 20px;">');
                results.push('1. 用户上传图片 → 图片验证');
                results.push('2. Google Vision API → 文字提取');
                results.push('3. DeepSeek LLM → 文字处理');
                results.push('4. 结构化订单数据 → 用户确认');
                results.push('5. GoMyHire API → 创建订单');
                results.push('</div>');
                
                resultsDiv.innerHTML = results.join('<br>');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">工作流测试失败: ${error.message}</div>`;
            }
        }

        // 运行所有集成测试
        async function runAllIntegrationTests() {
            const resultsDiv = document.getElementById('integrationResults');
            const progressBar = document.getElementById('testProgress');
            const summaryDiv = document.getElementById('testSummary');
            
            resultsDiv.innerHTML = '<div class="info">正在运行完整集成测试...</div>';
            progressBar.style.width = '0%';
            
            if (!initializeTester()) {
                resultsDiv.innerHTML = '<div class="error">测试器初始化失败</div>';
                return;
            }

            try {
                // 重定向日志输出到结果区域
                const originalLog = integrationTester.log;
                integrationTester.log = function(message) {
                    originalLog.call(this, message);
                    resultsDiv.innerHTML = this.results.join('<br>');
                    resultsDiv.scrollTop = resultsDiv.scrollHeight;
                };

                // 运行所有测试
                const testResult = await integrationTester.runAllTests();
                
                // 更新进度条
                progressBar.style.width = '100%';
                
                // 更新摘要
                const passedCount = integrationTester.results.filter(r => r.includes('✓')).length;
                const failedCount = integrationTester.results.filter(r => r.includes('✗')).length;
                const warningCount = integrationTester.results.filter(r => r.includes('⚠')).length;
                
                summaryDiv.innerHTML = `
                    <h3>测试完成</h3>
                    <p><span class="success">✓ 通过: ${passedCount}</span> | 
                       <span class="error">✗ 失败: ${failedCount}</span> | 
                       <span class="warning">⚠ 警告: ${warningCount}</span></p>
                    <p><strong>系统状态:</strong> ${testResult ? 
                        '<span class="success">✅ 准备就绪</span>' : 
                        '<span class="error">❌ 需要修复</span>'}</p>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">集成测试失败: ${error.message}</div>`;
                progressBar.style.width = '100%';
                progressBar.style.backgroundColor = '#dc3545';
            }
        }

        // 清除所有结果
        function clearAllResults() {
            const resultDivs = [
                'configResults', 'visionResults', 'llmResults', 
                'workflowResults', 'integrationResults'
            ];
            
            resultDivs.forEach(id => {
                const div = document.getElementById(id);
                if (div) div.innerHTML = '';
            });
            
            const progressBar = document.getElementById('testProgress');
            progressBar.style.width = '0%';
            progressBar.style.backgroundColor = '#007bff';
            
            const summaryDiv = document.getElementById('testSummary');
            summaryDiv.innerHTML = `
                <p>点击"运行所有测试"按钮开始完整的集成测试。</p>
                <p><strong>测试覆盖范围：</strong></p>
                <ul>
                    <li>✅ API配置验证（Google Vision, DeepSeek, Gemini）</li>
                    <li>✅ 图片处理工作流</li>
                    <li>✅ 文字提取和图像分析</li>
                    <li>✅ LLM服务选择和备份机制</li>
                    <li>✅ 端到端订单处理流程</li>
                </ul>
            `;
        }

        // 页面加载完成后运行基本配置检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                runConfigurationTests();
            }, 1000);
        });
    </script>
</body>
</html>
