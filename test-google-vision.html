<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Vision API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Google Vision API 集成测试</h1>
    
    <div class="test-section">
        <h2>配置测试</h2>
        <button onclick="testConfiguration()">测试配置</button>
        <div id="configResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>图片文字提取测试</h2>
        <input type="file" id="testImage" accept="image/*" title="选择测试图片">
        <button onclick="testTextExtraction()">提取文字</button>
        <div id="textResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>图片内容分析测试</h2>
        <button onclick="testImageAnalysis()">分析图片内容</button>
        <div id="analysisResult" class="result"></div>
    </div>

    <!-- 引入配置和应用文件 -->
    <script src="config.js"></script>
    <script src="logger.js"></script>

    <script>
        // 创建测试用的图片处理服务实例
        class TestImageService {
            constructor() {
                this.googleVisionConfig = SYSTEM_CONFIG.API.GOOGLE_VISION;
            }

            async fileToBase64(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const base64 = reader.result.split(',')[1];
                        resolve(base64);
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            }

            async callGoogleVisionAPI(base64Data, features = null) {
                if (!features) {
                    features = [
                        { 
                            type: this.googleVisionConfig.FEATURES.DOCUMENT_TEXT_DETECTION, 
                            maxResults: this.googleVisionConfig.MAX_RESULTS 
                        }
                    ];
                }

                const requestBody = {
                    requests: [
                        {
                            image: {
                                content: base64Data
                            },
                            features: features
                        }
                    ]
                };

                const response = await fetch(`${this.googleVisionConfig.API_URL}?key=${this.googleVisionConfig.API_KEY}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Google Vision API错误: ${response.status} ${response.statusText}\n${errorText}`);
                }

                const result = await response.json();
                
                if (result.responses && result.responses[0] && result.responses[0].error) {
                    const apiError = result.responses[0].error;
                    throw new Error(`Google Vision API响应错误: ${apiError.message}`);
                }

                return result;
            }

            parseVisionResponse(visionResult) {
                try {
                    const response = visionResult.responses[0];
                    
                    if (response.fullTextAnnotation && response.fullTextAnnotation.text) {
                        return response.fullTextAnnotation.text.trim();
                    }
                    
                    if (response.textAnnotations && response.textAnnotations.length > 0) {
                        return response.textAnnotations[0].description.trim();
                    }
                    
                    return '';
                } catch (error) {
                    console.error('解析响应失败:', error);
                    return '';
                }
            }

            async extractTextFromImage(imageFile) {
                const base64Data = await this.fileToBase64(imageFile);
                const visionResult = await this.callGoogleVisionAPI(base64Data);
                return this.parseVisionResponse(visionResult);
            }

            async analyzeImageContent(imageFile) {
                const base64Data = await this.fileToBase64(imageFile);
                
                const analysisResult = await this.callGoogleVisionAPI(base64Data, [
                    { type: this.googleVisionConfig.FEATURES.TEXT_DETECTION, maxResults: this.googleVisionConfig.MAX_RESULTS },
                    { type: this.googleVisionConfig.FEATURES.DOCUMENT_TEXT_DETECTION, maxResults: this.googleVisionConfig.MAX_RESULTS },
                    { type: this.googleVisionConfig.FEATURES.LABEL_DETECTION, maxResults: this.googleVisionConfig.MAX_RESULTS },
                    { type: this.googleVisionConfig.FEATURES.OBJECT_LOCALIZATION, maxResults: this.googleVisionConfig.MAX_RESULTS }
                ]);
                
                return {
                    text: this.parseVisionResponse(analysisResult),
                    labels: this.parseLabels(analysisResult),
                    objects: this.parseObjects(analysisResult),
                    rawResult: analysisResult
                };
            }

            parseLabels(visionResult) {
                try {
                    const response = visionResult.responses[0];
                    if (response.labelAnnotations) {
                        return response.labelAnnotations.map(label => ({
                            description: label.description,
                            score: label.score
                        }));
                    }
                    return [];
                } catch (error) {
                    console.error('解析标签失败:', error);
                    return [];
                }
            }

            parseObjects(visionResult) {
                try {
                    const response = visionResult.responses[0];
                    if (response.localizedObjectAnnotations) {
                        return response.localizedObjectAnnotations.map(obj => ({
                            name: obj.name,
                            score: obj.score,
                            boundingPoly: obj.boundingPoly
                        }));
                    }
                    return [];
                } catch (error) {
                    console.error('解析物体失败:', error);
                    return [];
                }
            }
        }

        const testImageService = new TestImageService();

        function testConfiguration() {
            const resultDiv = document.getElementById('configResult');
            resultDiv.innerHTML = '<div class="loading">正在测试配置...</div>';

            try {
                const config = SYSTEM_CONFIG.API.GOOGLE_VISION;
                let results = [];

                // 检查API Key
                if (config.API_KEY && config.API_KEY !== 'your-google-vision-api-key') {
                    results.push('<span class="success">✓ API Key 已配置</span>');
                } else {
                    results.push('<span class="error">✗ API Key 未配置或使用默认值</span>');
                }

                // 检查API URL
                if (config.API_URL && config.API_URL.includes('vision.googleapis.com')) {
                    results.push('<span class="success">✓ API URL 配置正确</span>');
                } else {
                    results.push('<span class="error">✗ API URL 配置错误</span>');
                }

                // 检查功能配置
                const features = config.FEATURES;
                if (features && features.TEXT_DETECTION && features.DOCUMENT_TEXT_DETECTION) {
                    results.push('<span class="success">✓ 功能配置正确</span>');
                } else {
                    results.push('<span class="error">✗ 功能配置缺失</span>');
                }

                resultDiv.innerHTML = results.join('<br>');

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">配置测试失败: ${error.message}</span>`;
            }
        }

        async function testTextExtraction() {
            const fileInput = document.getElementById('testImage');
            const resultDiv = document.getElementById('textResult');

            if (!fileInput.files.length) {
                resultDiv.innerHTML = '<span class="error">请先选择一张图片</span>';
                return;
            }

            const file = fileInput.files[0];
            resultDiv.innerHTML = '<div class="loading">正在提取文字...</div>';

            try {
                const extractedText = await testImageService.extractTextFromImage(file);
                
                if (extractedText) {
                    resultDiv.innerHTML = `
                        <span class="success">✓ 文字提取成功</span><br>
                        <strong>提取的文字:</strong><br>
                        <pre>${extractedText}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = '<span class="error">未能从图片中提取到文字</span>';
                }

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">文字提取失败: ${error.message}</span>`;
            }
        }

        async function testImageAnalysis() {
            const fileInput = document.getElementById('testImage');
            const resultDiv = document.getElementById('analysisResult');

            if (!fileInput.files.length) {
                resultDiv.innerHTML = '<span class="error">请先选择一张图片</span>';
                return;
            }

            const file = fileInput.files[0];
            resultDiv.innerHTML = '<div class="loading">正在分析图片内容...</div>';

            try {
                const analysis = await testImageService.analyzeImageContent(file);
                
                let results = [];
                
                if (analysis.text) {
                    results.push(`<strong>文字内容:</strong><br><pre>${analysis.text}</pre>`);
                }
                
                if (analysis.labels && analysis.labels.length > 0) {
                    const labelsHtml = analysis.labels.map(label => 
                        `${label.description} (${(label.score * 100).toFixed(1)}%)`
                    ).join(', ');
                    results.push(`<strong>图片标签:</strong><br>${labelsHtml}`);
                }
                
                if (analysis.objects && analysis.objects.length > 0) {
                    const objectsHtml = analysis.objects.map(obj => 
                        `${obj.name} (${(obj.score * 100).toFixed(1)}%)`
                    ).join(', ');
                    results.push(`<strong>检测物体:</strong><br>${objectsHtml}`);
                }

                if (results.length > 0) {
                    resultDiv.innerHTML = `<span class="success">✓ 图片分析成功</span><br><br>${results.join('<br><br>')}`;
                } else {
                    resultDiv.innerHTML = '<span class="error">图片分析完成，但未检测到内容</span>';
                }

            } catch (error) {
                resultDiv.innerHTML = `<span class="error">图片分析失败: ${error.message}</span>`;
            }
        }

        // 页面加载完成后自动测试配置
        window.onload = function() {
            testConfiguration();
        };
    </script>
</body>
</html>
