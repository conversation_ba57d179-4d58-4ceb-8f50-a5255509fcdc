/**
 * @file app.js - OTA订单处理系统主应用
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 全局配置 - 使用 SYSTEM_CONFIG 中的实际配置
const CONFIG = {
    API_BASE_URL: SYSTEM_CONFIG.API.BASE_URL,
    // DeepSeek 配置（主要LLM）
    DEEPSEEK_API_KEY: SYSTEM_CONFIG.API.DEEPSEEK.API_KEY,
    DEEPSEEK_API_URL: SYSTEM_CONFIG.API.DEEPSEEK.API_URL,
    DEEPSEEK_TIMEOUT: SYSTEM_CONFIG.API.DEEPSEEK.TIMEOUT,
    // Gemini 配置（后备LLM）
    GEMINI_API_KEY: SYSTEM_CONFIG.API.GEMINI.API_KEY,
    GEMINI_API_URL: SYSTEM_CONFIG.API.GEMINI.API_URL,
    // Google Vision 配置（图片分析）
    GOOGLE_VISION_API_KEY: SYSTEM_CONFIG.API.GOOGLE_VISION.API_KEY,
    GOOGLE_VISION_API_URL: SYSTEM_CONFIG.API.GOOGLE_VISION.API_URL,
    STORAGE_KEYS: SYSTEM_CONFIG.STORAGE_KEYS
};

// 应用状态管理
class AppState {
    constructor() {
        this.token = localStorage.getItem(CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.backendUsers = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.BACKEND_USERS) || '[]');
        this.subCategories = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.SUB_CATEGORIES) || '[]');
        this.carTypes = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.CAR_TYPES) || '[]');
        this.processedOrders = [];
    }

    /**
     * @function setToken - 设置认证令牌
     * @param {string} token - 认证令牌
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem(CONFIG.STORAGE_KEYS.TOKEN, token);
    }

    /**
     * @function setUserInfo - 设置用户信息
     * @param {object} userInfo - 用户信息对象
     */
    setUserInfo(userInfo) {
        this.userInfo = userInfo;
        localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
    }

    /**
     * @function clearAuth - 清除认证信息
     */
    clearAuth() {
        this.token = null;
        this.userInfo = null;
        localStorage.removeItem(CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_INFO);
    }

    /**
     * @function cacheSystemData - 缓存系统数据
     * @param {string} key - 缓存键
     * @param {any} data - 要缓存的数据
     */
    cacheSystemData(key, data) {
        this[key] = data;
        localStorage.setItem(CONFIG.STORAGE_KEYS[key.toUpperCase()], JSON.stringify(data));
    }
}

// API服务类
class ApiService {
    constructor(appState) {
        this.appState = appState;
    }

    /**
     * @function login - 用户登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Promise<object>} 登录结果
     */
    async login(email, password) {
        logger.info('API', '发起登录请求', { email, url: `${CONFIG.API_BASE_URL}/login` });
        
        try {
            const requestData = {
                email: email,
                password: password
            };
            
            logger.debug('API', '登录请求数据', requestData);
            
            const response = await axios.post(`${CONFIG.API_BASE_URL}/login`, requestData);
            
            logger.debug('API', '登录响应数据', {
                status: response.status,
                data: response.data
            });
            
            if (response.data.status && response.data.token) {
                // 提取实际token（去掉数字|前缀）
                const actualToken = response.data.token.split('|')[1] || response.data.token;
                
                logger.debug('API', 'Token处理完成', {
                    originalToken: response.data.token.substring(0, 20) + '...',
                    processedToken: actualToken.substring(0, 20) + '...'
                });
                
                this.appState.setToken(actualToken);
                this.appState.setUserInfo(response.data.user || { email });
                
                logger.success('API', '登录API调用成功');
                
                return { success: true, token: actualToken };
            } else {
                logger.error('API', '登录响应格式错误', response.data);
                throw new Error('登录失败：无效的响应');
            }
        } catch (error) {
            logger.error('API', '登录API调用失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '登录失败，请检查网络连接');
        }
    }

    /**
     * @function getBackendUsers - 获取后台用户列表
     * @returns {Promise<array>} 用户列表
     */
    async getBackendUsers() {
        logger.info('API', '发起获取后端用户请求');
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取后端用户失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            logger.debug('API', '发送后端用户请求', {
                url: `${CONFIG.API_BASE_URL}/backend_users`,
                hasToken: !!token
            });
            
            const response = await axios.get(`${CONFIG.API_BASE_URL}/backend_users`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            const users = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', `${CONFIG.API_BASE_URL}/backend_users`, response.status, {
                total_count: users.length,
                users: users
            });

            logger.debug('API', '后端用户数据结构分析', {
                status: response.status,
                dataLength: users.length,
                sampleUser: users.length > 0 ? users[0] : null,
                userFields: users.length > 0 ? Object.keys(users[0]) : []
            });

            this.appState.cacheSystemData('backendUsers', users);

            logger.success('API', '获取后端用户成功', {
                userCount: users.length,
                userNames: users.map(u => u.name || u.username || u.id).slice(0, 5)
            });
            return users;
        } catch (error) {
            logger.error('API', '获取后端用户失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取后端用户失败');
        }
    }

    /**
     * @function getSubCategories - 获取子分类列表
     * @returns {Promise<array>} 子分类列表
     */
    async getSubCategories() {
        logger.info('API', '发起获取子分类请求');
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取子分类失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            logger.debug('API', '发送子分类请求', {
                url: `${CONFIG.API_BASE_URL}/sub_category`,
                hasToken: !!token
            });
            
            const response = await axios.get(`${CONFIG.API_BASE_URL}/sub_category`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            const categories = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', `${CONFIG.API_BASE_URL}/sub_category`, response.status, {
                total_count: categories.length,
                categories: categories
            });

            logger.debug('API', '子分类数据结构分析', {
                status: response.status,
                dataLength: categories.length,
                sampleCategory: categories.length > 0 ? categories[0] : null,
                categoryFields: categories.length > 0 ? Object.keys(categories[0]) : []
            });

            this.appState.cacheSystemData('subCategories', categories);

            logger.success('API', '获取子分类成功', {
                categoryCount: categories.length,
                categoryNames: categories.map(c => c.name || c.title || c.id).slice(0, 5)
            });
            return categories;
        } catch (error) {
            logger.error('API', '获取子分类失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取子分类失败');
        }
    }

    /**
     * @function getCarTypes - 获取车型列表
     * @returns {Promise<array>} 车型列表
     */
    async getCarTypes() {
        logger.info('API', '发起获取车型请求');
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取车型失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            logger.debug('API', '发送车型请求', {
                url: `${CONFIG.API_BASE_URL}/car_types`,
                hasToken: !!token
            });
            
            const response = await axios.get(`${CONFIG.API_BASE_URL}/car_types`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            const carTypes = response.data.data || [];

            // 记录详细的API响应日志
            logger.logApiResponse('GET', `${CONFIG.API_BASE_URL}/car_types`, response.status, {
                total_count: carTypes.length,
                car_types: carTypes
            });

            logger.debug('API', '车型数据结构分析', {
                status: response.status,
                dataLength: carTypes.length,
                sampleCarType: carTypes.length > 0 ? carTypes[0] : null,
                carTypeFields: carTypes.length > 0 ? Object.keys(carTypes[0]) : []
            });

            this.appState.cacheSystemData('carTypes', carTypes);

            logger.success('API', '获取车型成功', {
                carTypeCount: carTypes.length,
                carTypeNames: carTypes.map(c => c.name || c.title || c.type || c.id).slice(0, 5)
            });
            return carTypes;
        } catch (error) {
            logger.error('API', '获取车型失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取车型失败');
        }
    }

    /**
     * @function createOrder - 创建订单
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 创建结果
     */
    async createOrder(orderData) {
        const requestStartTime = Date.now();
        const method = 'POST';
        const url = `${CONFIG.API_BASE_URL}/create_order`;

        logger.info('API', '发起创建订单请求', {
            hasData: !!orderData,
            dataKeys: Object.keys(orderData || {})
        });

        // 记录API请求日志
        const sanitizedOrderData = {
            ...orderData,
            // 隐藏敏感信息
            customer_phone: orderData.customer_phone ? '***' + orderData.customer_phone.slice(-4) : undefined,
            customer_email: orderData.customer_email ? orderData.customer_email.replace(/(.{2}).*(@.*)/, '$1***$2') : undefined
        };

        logger.logApiRequest(method, url, sanitizedOrderData, {
            'Content-Type': 'application/json'
        });

        try {
            const response = await axios.post(url, orderData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const responseTime = Date.now() - requestStartTime;

            // 记录API响应日志
            logger.logApiResponse(method, url, response.status, response.data, responseTime);

            logger.success('API', '创建订单成功', {
                responseTime: `${responseTime}ms`,
                orderId: response.data?.id || 'unknown'
            });

            return response.data;
        } catch (error) {
            const responseTime = Date.now() - requestStartTime;
            const status = error.response?.status || 0;

            // 记录API错误响应日志
            logger.logApiResponse(method, url, status, error.response?.data, responseTime);

            logger.error('API', '创建订单失败', {
                message: error.message,
                response: error.response?.data,
                status: status,
                responseTime: `${responseTime}ms`,
                orderData: sanitizedOrderData
            });

            throw new Error(error.response?.data?.message || '创建订单失败');
        }
    }
}

// LLM 服务管理类 - 管理 DeepSeek 和 Gemini
class LLMService {
    constructor() {
        // 初始化提示词管理器
        this.promptManager = new PromptManager();

        // DeepSeek 状态
        this.deepseekStatus = {
            connectionStatus: 'checking',
            lastCheckTime: null,
            consecutiveFailures: 0,
            isChecking: false,
            lastSuccessTime: null
        };

        // Gemini 状态
        this.geminiStatus = {
            connectionStatus: 'checking',
            lastCheckTime: null,
            consecutiveFailures: 0,
            isChecking: false,
            lastSuccessTime: null
        };

        // 当前使用的 LLM
        this.currentLLM = 'deepseek'; // deepseek 或 gemini
    }

    /**
     * @function checkDeepSeekConnection - 检测 DeepSeek API 连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkDeepSeekConnection() {
        // 防止重复检测
        if (this.deepseekStatus.isChecking) {
            logger.debug('DeepSeek', '连接检测正在进行中，跳过重复检测');
            return this.deepseekStatus.connectionStatus === 'connected';
        }

        this.deepseekStatus.isChecking = true;
        const previousStatus = this.deepseekStatus.connectionStatus;
        this.deepseekStatus.connectionStatus = 'checking';

        logger.info('DeepSeek', '开始检测 DeepSeek API 连接状态', {
            previousStatus: previousStatus,
            consecutiveFailures: this.deepseekStatus.consecutiveFailures,
            lastCheckTime: this.deepseekStatus.lastCheckTime
        });

        try {
            // 检查 API Key 是否存在
            if (!CONFIG.DEEPSEEK_API_KEY || CONFIG.DEEPSEEK_API_KEY === 'sk-your-deepseek-api-key-here') {
                logger.warn('DeepSeek', 'DeepSeek API Key 未配置');
                this.deepseekStatus.connectionStatus = 'disconnected';
                this.deepseekStatus.consecutiveFailures++;
                return false;
            }

            // 发送测试请求
            const startTime = Date.now();
            const response = await fetch(CONFIG.DEEPSEEK_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${CONFIG.DEEPSEEK_API_KEY}`
                },
                body: JSON.stringify({
                    model: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.model,
                    messages: [
                        {
                            role: 'user',
                            content: 'ping'
                        }
                    ],
                    max_tokens: 5,
                    temperature: 0.1
                }),
                signal: AbortSignal.timeout(10000) // 10秒超时
            });

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const wasDisconnected = previousStatus === 'disconnected';
                this.deepseekStatus.connectionStatus = 'connected';
                this.deepseekStatus.lastCheckTime = new Date();
                this.deepseekStatus.lastSuccessTime = new Date();
                this.deepseekStatus.consecutiveFailures = 0;

                logger.success('DeepSeek', 'DeepSeek API 连接成功', {
                    responseTime: `${responseTime}ms`,
                    statusChanged: wasDisconnected,
                    apiStatus: response.status
                });

                if (wasDisconnected) {
                    logger.info('DeepSeek', '🔄 DeepSeek API 连接已恢复');
                }

                return true;
            } else {
                this.deepseekStatus.connectionStatus = 'disconnected';
                this.deepseekStatus.consecutiveFailures++;

                logger.error('DeepSeek', `DeepSeek API 连接失败: ${response.status}`, {
                    responseTime: `${responseTime}ms`,
                    consecutiveFailures: this.deepseekStatus.consecutiveFailures,
                    statusText: response.statusText
                });

                return false;
            }
        } catch (error) {
            this.deepseekStatus.connectionStatus = 'disconnected';
            this.deepseekStatus.consecutiveFailures++;

            logger.error('DeepSeek', 'DeepSeek API 连接检测失败', {
                error: error.message,
                consecutiveFailures: this.deepseekStatus.consecutiveFailures,
                errorType: error.name
            });

            return false;
        } finally {
            this.deepseekStatus.isChecking = false;
            this.deepseekStatus.lastCheckTime = new Date();
        }
    }

    /**
     * @function checkGeminiConnection - 检测 Gemini API 连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkGeminiConnection() {
        // 防止重复检测
        if (this.geminiStatus.isChecking) {
            logger.debug('Gemini', '连接检测正在进行中，跳过重复检测');
            return this.geminiStatus.connectionStatus === 'connected';
        }

        this.geminiStatus.isChecking = true;
        const previousStatus = this.geminiStatus.connectionStatus;
        this.geminiStatus.connectionStatus = 'checking';

        logger.info('Gemini', '开始检测 Gemini API 连接状态', {
            previousStatus: previousStatus,
            consecutiveFailures: this.geminiStatus.consecutiveFailures,
            lastCheckTime: this.geminiStatus.lastCheckTime
        });

        try {
            // 检查 API Key 是否存在
            if (!CONFIG.GEMINI_API_KEY) {
                logger.warn('Gemini', 'Gemini API Key 未配置');
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;
                return false;
            }

            // 发送测试请求
            const startTime = Date.now();
            const response = await fetch(
                `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: 'ping'
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 5
                        }
                    }),
                    signal: AbortSignal.timeout(10000) // 10秒超时
                }
            );

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const wasDisconnected = previousStatus === 'disconnected';
                this.geminiStatus.connectionStatus = 'connected';
                this.geminiStatus.lastCheckTime = new Date();
                this.geminiStatus.lastSuccessTime = new Date();
                this.geminiStatus.consecutiveFailures = 0;

                logger.success('Gemini', 'Gemini API 连接成功', {
                    responseTime: `${responseTime}ms`,
                    statusChanged: wasDisconnected,
                    apiStatus: response.status
                });

                if (wasDisconnected) {
                    logger.info('Gemini', '🔄 Gemini API 连接已恢复');
                }

                return true;
            } else {
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;

                logger.error('Gemini', `Gemini API 连接失败: ${response.status}`, {
                    responseTime: `${responseTime}ms`,
                    consecutiveFailures: this.geminiStatus.consecutiveFailures,
                    statusText: response.statusText
                });

                return false;
            }
        } catch (error) {
            this.geminiStatus.connectionStatus = 'disconnected';
            this.geminiStatus.consecutiveFailures++;

            logger.error('Gemini', 'Gemini API 连接检测失败', {
                error: error.message,
                consecutiveFailures: this.geminiStatus.consecutiveFailures,
                errorType: error.name
            });

            return false;
        } finally {
            this.geminiStatus.isChecking = false;
            this.geminiStatus.lastCheckTime = new Date();
        }
    }

    /**
     * @function getDeepSeekStatus - 获取 DeepSeek 连接状态
     * @returns {object} 状态信息
     */
    getDeepSeekStatus() {
        return {
            status: this.deepseekStatus.connectionStatus,
            text: this.getDeepSeekStatusText(),
            consecutiveFailures: this.deepseekStatus.consecutiveFailures,
            lastCheckTime: this.deepseekStatus.lastCheckTime
        };
    }

    /**
     * @function getGeminiStatus - 获取 Gemini 连接状态
     * @returns {object} 状态信息
     */
    getGeminiStatus() {
        return {
            status: this.geminiStatus.connectionStatus,
            text: this.getGeminiStatusText(),
            consecutiveFailures: this.geminiStatus.consecutiveFailures,
            lastCheckTime: this.geminiStatus.lastCheckTime
        };
    }

    /**
     * @function getDeepSeekStatusText - 获取 DeepSeek 状态文字描述
     * @returns {string} 状态描述
     */
    getDeepSeekStatusText() {
        switch (this.deepseekStatus.connectionStatus) {
            case 'connected':
                return 'DeepSeek 已连接';
            case 'disconnected':
                return 'DeepSeek 连接失败';
            case 'checking':
            default:
                return 'DeepSeek 检测中...';
        }
    }

    /**
     * @function getGeminiStatusText - 获取 Gemini 状态文字描述
     * @returns {string} 状态描述
     */
    getGeminiStatusText() {
        switch (this.geminiStatus.connectionStatus) {
            case 'connected':
                return 'Gemini 已连接';
            case 'disconnected':
                return 'Gemini 连接失败';
            case 'checking':
            default:
                return 'Gemini 检测中...';
        }
    }

    /**
     * @function selectBestLLM - 选择最佳可用的 LLM
     * @returns {string} 选择的 LLM ('deepseek' 或 'gemini')
     */
    selectBestLLM() {
        // 优先使用 DeepSeek
        if (this.deepseekStatus.connectionStatus === 'connected') {
            this.currentLLM = 'deepseek';
            return 'deepseek';
        }

        // DeepSeek 不可用时使用 Gemini
        if (this.geminiStatus.connectionStatus === 'connected') {
            this.currentLLM = 'gemini';
            return 'gemini';
        }

        // 都不可用时，默认尝试 DeepSeek
        this.currentLLM = 'deepseek';
        return 'deepseek';
    }

    /**
     * @function getCurrentLLM - 获取当前使用的 LLM
     * @returns {string} 当前 LLM
     */
    getCurrentLLM() {
        return this.currentLLM;
    }

    /**
     * @function detectOTATypeLocally - 本地识别OTA类型
     * @param {string} orderContent - 订单内容
     * @returns {Object} 本地识别结果
     */
    detectOTATypeLocally(orderContent) {
        const content = orderContent.toLowerCase();
        
        // Chong Dealer 特征识别
        const chongFeatures = [
            /ota[：:].{0,5}chong/i,
            /chong.{0,5}车头/i,
            /chong.{0,5}dealer/i,
            /重庆.{0,5}经销商/i,
            /航班.{0,5}[a-z]{2}\d{3,4}/i,
            /接机.{0,10}\d{1,2}[.:]\d{2}/i,
            /送机.{0,10}\d{1,2}[.:]\d{2}/i
        ];
        
        let chongScore = 0;
        let detectedFeatures = [];
        
        // 检测 Chong Dealer 特征
        chongFeatures.forEach(pattern => {
            if (pattern.test(content)) {
                chongScore += 1;
                detectedFeatures.push('Chong Dealer特征');
            }
        });
        

        
        // 判断结果
        let otaType = 'auto';
        let confidence = 0;
        let reasoning = '未检测到明确的OTA特征';
        
        if (chongScore >= 2) {
            otaType = 'chong-dealer';
            confidence = Math.min(0.9, 0.5 + (chongScore * 0.1));
            reasoning = `检测到${chongScore}个Chong Dealer特征`;
        } else if (chongScore > 0) {
            otaType = 'chong-dealer';
            confidence = 0.4;
            reasoning = '检测到部分Chong Dealer特征，但不够明确';
        }
        
        return {
            ota_type: otaType,
            confidence: confidence,
            reasoning: reasoning,
            key_features: detectedFeatures,
            scores: { chong: chongScore }
        };
    }

    /**
     * @function processOrderWithAI - 使用AI处理订单
     * @param {string} orderContent - 订单内容
     * @param {string} otaType - OTA类型
     * @returns {Promise<string>} 处理结果
     */
    async processOrderWithAI(orderContent, otaType = 'chong-dealer') {
        logger.info('AI', '开始AI订单处理', {
            contentLength: orderContent?.length || 0,
            otaType: otaType,
            currentLLM: this.currentLLM
        });

        try {
            // 选择最佳可用的 LLM
            const selectedLLM = this.selectBestLLM();

            logger.info('AI', `使用 ${selectedLLM.toUpperCase()} 进行订单处理`, {
                deepseekStatus: this.deepseekStatus.connectionStatus,
                geminiStatus: this.geminiStatus.connectionStatus
            });

            // 使用提示词管理器获取对应的提示词
            const currentDate = new Date().toISOString().split('T')[0];
            const prompt = this.promptManager.getOTAPrompt(otaType, orderContent, currentDate);

            // 尝试使用选择的 LLM 处理，如果失败则切换到备用
            let result;
            try {
                if (selectedLLM === 'deepseek') {
                    result = await this.processWithDeepSeek(prompt);
                } else {
                    result = await this.processWithGemini(prompt);
                }
                return result;
            } catch (error) {
                logger.warn('AI', `${selectedLLM.toUpperCase()} 处理失败，尝试切换到备用 LLM`, {
                    error: error.message
                });

                // 切换到备用 LLM
                const fallbackLLM = selectedLLM === 'deepseek' ? 'gemini' : 'deepseek';
                logger.info('AI', `切换到备用 LLM: ${fallbackLLM.toUpperCase()}`);

                if (fallbackLLM === 'deepseek') {
                    result = await this.processWithDeepSeek(prompt);
                } else {
                    result = await this.processWithGemini(prompt);
                }
                return result;
            }
        } catch (error) {
            logger.error('AI', 'AI订单处理失败', {
                message: error.message,
                stack: error.stack,
                otaType: otaType
            });
            throw new Error('AI处理失败，请检查API配置或网络连接');
        }
    }

    /**
     * @function processWithDeepSeek - 使用 DeepSeek 处理订单
     * @param {string} prompt - 提示词
     * @returns {Promise<string>} 处理结果
     */
    async processWithDeepSeek(prompt) {
        logger.debug('DeepSeek', '构建 DeepSeek 请求', {
            promptLength: prompt?.length || 0,
            apiUrl: CONFIG.DEEPSEEK_API_URL,
            timeout: CONFIG.DEEPSEEK_TIMEOUT
        });

        const response = await fetch(CONFIG.DEEPSEEK_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.DEEPSEEK_API_KEY}`
            },
            body: JSON.stringify({
                model: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.temperature,
                max_tokens: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.max_tokens,
                top_p: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.top_p
            }),
            signal: AbortSignal.timeout(CONFIG.DEEPSEEK_TIMEOUT) // 15秒超时
        });

        logger.debug('DeepSeek', 'DeepSeek 响应状态', {
            status: response.status,
            ok: response.ok
        });

        if (!response.ok) {
            logger.error('DeepSeek', 'DeepSeek API 请求失败', {
                status: response.status,
                statusText: response.statusText
            });
            throw new Error(`DeepSeek API 错误: ${response.status}`);
        }

        const data = await response.json();

        logger.debug('DeepSeek', 'DeepSeek 响应数据', {
            hasChoices: !!data.choices,
            choicesLength: data.choices?.length || 0
        });

        if (data.choices && data.choices[0] && data.choices[0].message) {
            const result = data.choices[0].message.content;
            logger.success('DeepSeek', 'DeepSeek 订单处理成功', {
                resultLength: result?.length || 0
            });
            return result;
        } else {
            logger.error('DeepSeek', 'DeepSeek 处理失败：无效响应', data);
            throw new Error('DeepSeek 处理失败：无效响应');
        }
    }

    /**
     * @function processWithGemini - 使用 Gemini 处理订单
     * @param {string} prompt - 提示词
     * @returns {Promise<string>} 处理结果
     */
    async processWithGemini(prompt) {
        logger.debug('Gemini', '构建 Gemini 请求', {
            promptLength: prompt?.length || 0,
            apiUrl: CONFIG.GEMINI_API_URL
        });

        const response = await fetch(
            `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.temperature,
                        topK: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.topK,
                        topP: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.topP,
                        maxOutputTokens: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.maxOutputTokens
                    }
                })
            }
        );

        logger.debug('Gemini', 'Gemini 响应状态', {
            status: response.status,
            ok: response.ok
        });

        if (!response.ok) {
            logger.error('Gemini', 'Gemini API 请求失败', {
                status: response.status,
                statusText: response.statusText
            });
            throw new Error(`Gemini API 错误: ${response.status}`);
        }

        const data = await response.json();

        logger.debug('Gemini', 'Gemini 响应数据', {
            hasCandidates: !!data.candidates,
            candidatesLength: data.candidates?.length || 0
        });

        if (data.candidates && data.candidates[0]) {
            const result = data.candidates[0].content.parts[0].text;
            logger.success('Gemini', 'Gemini 订单处理成功', {
                resultLength: result?.length || 0
            });
            return result;
        } else {
            logger.error('Gemini', 'Gemini 处理失败：无效响应', data);
            throw new Error('Gemini 处理失败：无效响应');
        }
    }

    /**
     * @function detectOTAType - 检测订单的OTA类型
     * @param {string} orderContent - 订单内容
     * @returns {Promise<Object>} OTA类型识别结果
     */
    async detectOTAType(orderContent) {
        logger.info('AI', '开始OTA类型识别', {
            contentLength: orderContent?.length || 0
        });
        
        try {
            const prompt = this.promptManager.getDetectionPrompt(orderContent);
            
            logger.debug('AI', '构建OTA识别请求', {
                promptLength: prompt?.length || 0
            });
            
            const response = await fetch(
                `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.1,
                            topK: 1,
                            topP: 1,
                            maxOutputTokens: 1024
                        }
                    })
                }
            );
            
            logger.debug('AI', 'OTA识别响应状态', {
                status: response.status,
                ok: response.ok
            });
            
            if (!response.ok) {
                logger.error('AI', 'OTA识别API请求失败', {
                    status: response.status,
                    statusText: response.statusText
                });
                throw new Error(`OTA识别API错误: ${response.status}`);
            }
            
            const data = await response.json();
            const result = data.candidates[0].content.parts[0].text;
            
            logger.debug('AI', 'OTA识别原始结果', {
                resultLength: result?.length || 0,
                resultPreview: result?.substring(0, 100) + '...'
            });
            
            // 尝试解析JSON结果
            try {
                const parsedResult = JSON.parse(result);
                logger.success('AI', 'OTA类型识别成功', {
                    otaType: parsedResult.ota_type,
                    confidence: parsedResult.confidence,
                    reasoning: parsedResult.reasoning
                });
                return parsedResult;
            } catch (parseError) {
                logger.warn('AI', 'OTA类型识别结果解析失败，使用默认值', {
                    parseError: parseError.message,
                    rawResult: result
                });
                return {
                    ota_type: 'auto',
                    confidence: 0.5,
                    reasoning: '自动识别失败，使用默认类型',
                    key_features: []
                };
            }
        } catch (error) {
            logger.error('AI', 'OTA类型识别失败', {
                message: error.message,
                stack: error.stack
            });
            return {
                ota_type: 'auto',
                confidence: 0,
                reasoning: '识别过程出错',
                key_features: []
            };
        }
    }

    /**
     * @function processImageWithAI - 使用AI处理图片订单
     * @param {string} imageData - 图片数据（base64）
     * @returns {Promise<string>} 处理后的订单信息
     */
    async processImageWithAI(imageData) {
        try {
            const currentDate = new Date().toISOString().split('T')[0];
            const prompt = this.promptManager.getImageOCRPrompt(currentDate);
            
            const response = await fetch(
                `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [
                                {
                                    text: prompt
                                },
                                {
                                    inline_data: {
                                        mime_type: 'image/jpeg',
                                        data: imageData
                                    }
                                }
                            ]
                        }],
                        generationConfig: {
                            temperature: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.temperature,
                            topK: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.topK,
                            topP: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG.topP,
                            maxOutputTokens: 4096
                        }
                    })
                }
            );
            
            const data = await response.json();
            return data.candidates[0].content.parts[0].text;
        } catch (error) {
            console.error('图片AI处理失败:', error);
            throw new Error('图片处理失败，请重试');
        }
    }

    /**
     * @function getAvailablePromptTypes - 获取所有可用的提示词类型
     * @returns {Array} 提示词类型列表
     */
    getAvailablePromptTypes() {
        return this.promptManager.getAvailableTypes();
    }

    /**
     * @function detectMeetAndGreetService - 识别举牌服务需求
     * @param {string} orderContent - 订单内容
     * @returns {Object} 举牌服务识别结果
     */
    detectMeetAndGreetService(orderContent) {
        logger.info('AI', '开始举牌服务识别', {
            contentLength: orderContent?.length || 0
        });

        const content = orderContent.toLowerCase();

        // 举牌服务关键词列表
        const meetAndGreetKeywords = [
            /举牌/g,
            /接机牌/g,
            /举接机牌/g,
            /meet\s*and\s*greet/gi,
            /meet\s*&\s*greet/gi,
            /接机服务/g,
            /迎接服务/g,
            /举牌接机/g,
            /接机举牌/g,
            /名牌接机/g
        ];

        let detectedKeywords = [];
        let keywordCount = 0;

        // 检测关键词
        meetAndGreetKeywords.forEach(keyword => {
            const matches = content.match(keyword);
            if (matches) {
                keywordCount += matches.length;
                detectedKeywords.push(keyword.source || keyword.toString());
            }
        });

        // 提取客人姓名
        let customerName = '';
        const namePatterns = [
            /姓名[：:]\s*([^\n\r,，]+)/i,
            /客人姓名[：:]\s*([^\n\r,，]+)/i,
            /联系人[：:]\s*([^\n\r,，]+)/i,
            /客人[：:]\s*([^\n\r,，]+)/i,
            /乘客[：:]\s*([^\n\r,，]+)/i
        ];

        for (const pattern of namePatterns) {
            const match = orderContent.match(pattern);
            if (match && match[1]) {
                customerName = match[1].trim();
                break;
            }
        }

        // 判断是否需要举牌服务
        const needsMeetAndGreet = keywordCount > 0;
        const confidence = Math.min(0.9, keywordCount * 0.3);

        const result = {
            needsMeetAndGreet: needsMeetAndGreet,
            confidence: confidence,
            customerName: customerName,
            detectedKeywords: detectedKeywords,
            keywordCount: keywordCount,
            reasoning: needsMeetAndGreet
                ? `检测到${keywordCount}个举牌服务关键词`
                : '未检测到举牌服务需求'
        };

        logger.debug('AI', '举牌服务识别结果', result);

        if (needsMeetAndGreet) {
            logger.success('AI', '检测到举牌服务需求', {
                customerName: customerName,
                keywordCount: keywordCount
            });
        }

        return result;
    }
}

// 图片处理服务
class ImageService {
    constructor() {
        this.googleVisionConfig = SYSTEM_CONFIG.API.GOOGLE_VISION;
    }

    /**
     * @function extractTextFromImage - 使用Google Vision API从图片中提取文字
     * @param {File} imageFile - 图片文件
     * @returns {Promise<string>} 提取的文字
     */
    async extractTextFromImage(imageFile) {
        try {
            logger.info('图片处理', '开始使用Google Vision API提取文字', {
                fileName: imageFile.name,
                fileSize: imageFile.size,
                fileType: imageFile.type
            });

            // 将图片转换为base64
            const base64Data = await this.fileToBase64(imageFile);
            
            // 调用Google Vision API
            const visionResult = await this.callGoogleVisionAPI(base64Data);
            
            // 提取文字内容
            const extractedText = this.parseVisionResponse(visionResult);
            
            logger.success('图片处理', 'Google Vision API文字提取成功', {
                extractedLength: extractedText.length,
                preview: extractedText.substring(0, 100) + '...'
            });

            return extractedText;
            
        } catch (error) {
            logger.error('图片处理', 'Google Vision API文字提取失败', {
                error: error.message,
                fileName: imageFile.name
            });
            
            // 如果Google Vision失败，返回提示
            throw new Error(`图片文字提取失败: ${error.message}`);
        }
    }

    /**
     * @function analyzeImageContent - 使用Google Vision API分析图片内容
     * @param {File} imageFile - 图片文件
     * @returns {Promise<Object>} 图片分析结果
     */
    async analyzeImageContent(imageFile) {
        try {
            logger.info('图片分析', '开始使用Google Vision API分析图片内容');

            // 将图片转换为base64
            const base64Data = await this.fileToBase64(imageFile);
            
            // 调用Google Vision API进行全面分析
            const analysisResult = await this.callGoogleVisionAPI(base64Data, [
                { type: this.googleVisionConfig.FEATURES.TEXT_DETECTION, maxResults: this.googleVisionConfig.MAX_RESULTS },
                { type: this.googleVisionConfig.FEATURES.DOCUMENT_TEXT_DETECTION, maxResults: this.googleVisionConfig.MAX_RESULTS },
                { type: this.googleVisionConfig.FEATURES.LABEL_DETECTION, maxResults: this.googleVisionConfig.MAX_RESULTS },
                { type: this.googleVisionConfig.FEATURES.OBJECT_LOCALIZATION, maxResults: this.googleVisionConfig.MAX_RESULTS }
            ]);
            
            logger.success('图片分析', 'Google Vision API分析完成');
            
            return {
                text: this.parseVisionResponse(analysisResult),
                labels: this.parseLabels(analysisResult),
                objects: this.parseObjects(analysisResult),
                rawResult: analysisResult
            };
            
        } catch (error) {
            logger.error('图片分析', 'Google Vision API分析失败', {
                error: error.message
            });
            throw new Error(`图片分析失败: ${error.message}`);
        }
    }

    /**
     * @function callGoogleVisionAPI - 调用Google Vision API
     * @param {string} base64Data - base64图片数据
     * @param {Array} features - 要使用的功能列表
     * @returns {Promise<Object>} API响应结果
     */
    async callGoogleVisionAPI(base64Data, features = null) {
        // 默认使用文档文字检测
        if (!features) {
            features = [
                { 
                    type: this.googleVisionConfig.FEATURES.DOCUMENT_TEXT_DETECTION, 
                    maxResults: this.googleVisionConfig.MAX_RESULTS 
                }
            ];
        }

        const requestBody = {
            requests: [
                {
                    image: {
                        content: base64Data
                    },
                    features: features
                }
            ]
        };

        logger.debug('图片处理', 'Google Vision API请求', {
            url: `${this.googleVisionConfig.API_URL}?key=${this.googleVisionConfig.API_KEY.substring(0, 10)}...`,
            features: features.map(f => f.type)
        });

        const response = await fetch(`${this.googleVisionConfig.API_URL}?key=${this.googleVisionConfig.API_KEY}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            logger.error('图片处理', 'Google Vision API请求失败', {
                status: response.status,
                statusText: response.statusText,
                error: errorText
            });
            throw new Error(`Google Vision API错误: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        
        // 检查API响应中的错误
        if (result.responses && result.responses[0] && result.responses[0].error) {
            const apiError = result.responses[0].error;
            logger.error('图片处理', 'Google Vision API响应错误', apiError);
            throw new Error(`Google Vision API错误: ${apiError.message}`);
        }

        return result;
    }

    /**
     * @function parseVisionResponse - 解析Vision API响应中的文字
     * @param {Object} visionResult - Vision API响应
     * @returns {string} 提取的文字
     */
    parseVisionResponse(visionResult) {
        try {
            const response = visionResult.responses[0];
            
            // 优先使用文档文字检测结果
            if (response.fullTextAnnotation && response.fullTextAnnotation.text) {
                return response.fullTextAnnotation.text.trim();
            }
            
            // 备用：使用普通文字检测结果
            if (response.textAnnotations && response.textAnnotations.length > 0) {
                return response.textAnnotations[0].description.trim();
            }
            
            return '';
            
        } catch (error) {
            logger.error('图片处理', '解析Vision API响应失败', {
                error: error.message
            });
            return '';
        }
    }

    /**
     * @function parseLabels - 解析Vision API响应中的标签
     * @param {Object} visionResult - Vision API响应
     * @returns {Array} 标签数组
     */
    parseLabels(visionResult) {
        try {
            const response = visionResult.responses[0];
            if (response.labelAnnotations) {
                return response.labelAnnotations.map(label => ({
                    description: label.description,
                    score: label.score
                }));
            }
            return [];
        } catch (error) {
            logger.error('图片处理', '解析标签失败', error);
            return [];
        }
    }

    /**
     * @function parseObjects - 解析Vision API响应中的物体
     * @param {Object} visionResult - Vision API响应
     * @returns {Array} 物体数组
     */
    parseObjects(visionResult) {
        try {
            const response = visionResult.responses[0];
            if (response.localizedObjectAnnotations) {
                return response.localizedObjectAnnotations.map(obj => ({
                    name: obj.name,
                    score: obj.score,
                    boundingPoly: obj.boundingPoly
                }));
            }
            return [];
        } catch (error) {
            logger.error('图片处理', '解析物体失败', error);
            return [];
        }
    }

    /**
     * @function fileToBase64 - 将文件转换为base64
     * @param {File} file - 文件对象
     * @returns {Promise<string>} base64数据（不包含数据URI前缀）
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                // 移除data:image/...;base64,前缀
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * @function validateImageFile - 验证图片文件
     * @param {File} file - 图片文件
     * @returns {Object} 验证结果
     */
    validateImageFile(file) {
        const errors = [];
        const allowedTypes = SYSTEM_CONFIG.UPLOAD.ALLOWED_TYPES;
        const maxSize = SYSTEM_CONFIG.UPLOAD.MAX_FILE_SIZE;

        // 检查文件类型
        if (!allowedTypes.includes(file.type)) {
            errors.push(`不支持的文件格式: ${file.type}`);
        }

        // 检查文件大小
        if (file.size > maxSize) {
            errors.push(`文件大小超过限制: ${(file.size / 1024 / 1024).toFixed(2)}MB > ${maxSize / 1024 / 1024}MB`);
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// 智能选择服务
class SmartSelectionService {
    constructor(appState) {
        this.appState = appState;
    }

    /**
     * @function selectBackendUser - 智能选择后台用户
     * @param {object} order - 订单对象
     * @returns {number} 后台用户ID
     */
    selectBackendUser(order) {
        logger.info('智能选择', '开始选择后台用户', {
            orderId: order.id,
            otaType: order.ota_type,
            availableUsers: this.appState.backendUsers.length
        });

        const users = this.appState.backendUsers;
        if (!users || users.length === 0) {
            logger.warn('智能选择', '无可用后台用户，使用默认值');
            return 338; // 默认使用指定的用户ID
        }

        // 优先选择指定的用户ID 338 (jcy1)
        let selectedUser = users.find(user => user.id === 338);
        let selectionReason = '';

        if (selectedUser) {
            selectionReason = '使用指定的默认用户 jcy1 (ID: 338)';
        } else {
            // 如果指定用户不存在，根据OTA类型和角色选择
            if (order.ota_type === 'Chong Dealer') {
                // 为Chong Dealer类型寻找专门的用户
                selectedUser = users.find(user =>
                    user.name?.toLowerCase().includes('chong') ||
                    user.name?.toLowerCase().includes('jcy') ||
                    user.role === 'Sub_Admin'
                );
                if (selectedUser) {
                    selectionReason = '找到Chong Dealer适用的Sub_Admin用户';
                }
            }

            // 如果还没找到，优先选择Sub_Admin角色
            if (!selectedUser) {
                selectedUser = users.find(user => user.role === 'Sub_Admin');
                if (selectedUser) {
                    selectionReason = '选择Sub_Admin角色用户';
                }
            }

            // 最后选择第一个Operator
            if (!selectedUser) {
                selectedUser = users.find(user => user.role === 'Operator') || users[0];
                selectionReason = selectedUser?.role === 'Operator' ? '选择Operator角色用户' : '使用第一个可用用户';
            }
        }

        logger.success('智能选择', '后台用户选择完成', {
            selectedUserId: selectedUser.id,
            selectedUserName: selectedUser.name,
            selectedUserRole: selectedUser.role,
            selectionReason: selectionReason
        });

        return selectedUser.id;
    }

    /**
     * @function selectSubCategory - 智能选择子分类
     * @param {object} order - 订单对象
     * @returns {number} 子分类ID
     */
    selectSubCategory(order) {
        logger.info('智能选择', '开始选择子分类', {
            orderId: order.id,
            serviceType: order.parsed?.['服务类型'],
            pickup: order.parsed?.['pickup'],
            drop: order.parsed?.['drop'],
            availableCategories: this.appState.subCategories.length
        });

        const categories = this.appState.subCategories;
        if (!categories || categories.length === 0) {
            logger.warn('智能选择', '无可用子分类，使用默认值');
            return 7; // 默认使用Pickup (ID: 7)
        }

        let selectedCategory = null;
        let selectionReason = '';

        const serviceType = order.parsed?.['服务类型'];
        const pickup = order.parsed?.['pickup'];
        const drop = order.parsed?.['drop'];
        const hasFlightInfo = !!order.parsed?.['航班'];

        // 判断服务类型
        const isPickup = serviceType === '接机' || pickup === 'klia' || hasFlightInfo;
        const isDropoff = serviceType === '送机' || drop === 'klia';
        const isCharter = serviceType === '包车' || (!isPickup && !isDropoff);

        // 根据真实的子分类数据进行智能选择
        if (isPickup) {
            // 接机服务 - 选择 Pickup (ID: 7)
            selectedCategory = categories.find(cat => cat.name === 'Pickup' && cat.main_category === 'Airport');
            if (selectedCategory) {
                selectionReason = '检测到接机服务，选择Airport-Pickup子分类';
            }
        } else if (isDropoff) {
            // 送机服务 - 选择 Dropoff (ID: 8)
            selectedCategory = categories.find(cat => cat.name === 'Dropoff' && cat.main_category === 'Airport');
            if (selectedCategory) {
                selectionReason = '检测到送机服务，选择Airport-Dropoff子分类';
            }
        } else if (isCharter) {
            // 包车服务 - 根据目的地选择
            const destination = order.parsed?.['目的地'] || order.parsed?.['drop'] || '';

            if (destination.toLowerCase().includes('genting')) {
                selectedCategory = categories.find(cat => cat.name === 'KL to genting');
                selectionReason = '检测到云顶目的地，选择KL to genting子分类';
            } else if (destination.toLowerCase().includes('melaka') || destination.toLowerCase().includes('马六甲')) {
                selectedCategory = categories.find(cat => cat.name === 'KL to melaka');
                selectionReason = '检测到马六甲目的地，选择KL to melaka子分类';
            } else if (destination.toLowerCase().includes('sekinchan')) {
                selectedCategory = categories.find(cat => cat.name === 'Sekinchan');
                selectionReason = '检测到适耕庄目的地，选择Sekinchan子分类';
            } else {
                // 通用包车服务
                selectedCategory = categories.find(cat => cat.name === 'Charter' && cat.main_category === 'Chartered');
                selectionReason = '检测到包车服务，选择通用Charter子分类';
            }
        }

        // 特殊情况：携程订单
        if (order.ota_type === 'Chong Dealer' || order.parsed?.['OTA']?.includes('携程')) {
            const ctripCategory = categories.find(cat => cat.name === '携程1');
            if (ctripCategory) {
                selectedCategory = ctripCategory;
                selectionReason = '检测到携程订单，选择携程1子分类';
            }
        }

        // 如果没有找到专用子分类，使用默认值
        if (!selectedCategory) {
            selectedCategory = categories.find(cat => cat.name === 'default') ||
                             categories.find(cat => cat.name === 'Pickup') ||
                             categories[0];
            selectionReason = '未找到匹配的子分类，使用默认子分类';
        }

        logger.success('智能选择', '子分类选择完成', {
            selectedCategoryId: selectedCategory.id,
            selectedCategoryName: selectedCategory.name,
            selectedMainCategory: selectedCategory.main_category,
            serviceType: serviceType,
            isPickup: isPickup,
            isDropoff: isDropoff,
            isCharter: isCharter,
            selectionReason: selectionReason
        });

        return selectedCategory.id;
    }

    /**
     * @function selectCarType - 智能选择车型
     * @param {object} order - 订单对象
     * @returns {number} 车型ID
     */
    selectCarType(order) {
        logger.info('智能选择', '开始选择车型', {
            orderId: order.id,
            passengerNumber: order.parsed?.['人数'],
            luggageNumber: order.parsed?.['行李数'],
            specifiedCarType: order.parsed?.['车型'],
            availableCarTypes: this.appState.carTypes.length
        });

        const carTypes = this.appState.carTypes;
        if (!carTypes || carTypes.length === 0) {
            logger.warn('智能选择', '无可用车型，使用默认值');
            return 6; // 默认使用Comfort 5 Seater (ID: 6)
        }

        let selectedCarType = null;
        let selectionReason = '';

        // 检查是否明确指定了车型
        const specifiedCarType = order.parsed?.['车型'] || order.parsed?.['车辆类型'] || order.parsed?.['vehicle_type'];

        if (specifiedCarType) {
            // 如果订单中明确指定了车型，优先匹配指定车型
            selectedCarType = carTypes.find(car =>
                car.type?.toLowerCase().includes(specifiedCarType.toLowerCase()) ||
                car.type?.includes(specifiedCarType)
            );
            if (selectedCarType) {
                selectionReason = `订单指定车型: ${specifiedCarType}`;
                logger.success('智能选择', '车型选择完成', {
                    selectedCarTypeId: selectedCarType.id,
                    selectedCarTypeName: selectedCarType.type,
                    selectedSeatNumber: selectedCarType.seat_number,
                    selectedPriority: selectedCarType.priority,
                    specifiedCarType: specifiedCarType,
                    selectionReason: selectionReason
                });
                return selectedCarType.id;
            }
        }

        // 如果没有指定车型，则根据人数进行判断
        const passengerNumberStr = order.parsed?.['人数'];
        let passengerNumber = 0;

        if (passengerNumberStr) {
            // 提取人数数字
            const numberMatch = passengerNumberStr.toString().match(/\d+/);
            passengerNumber = numberMatch ? parseInt(numberMatch[0]) : 0;
        }

        const luggageNumber = parseInt(order.parsed?.['行李数']) || 0;

        logger.debug('智能选择', '车型选择参数分析', {
            originalPassengerStr: passengerNumberStr,
            extractedPassengerNumber: passengerNumber,
            luggageNumber: luggageNumber,
            hasSpecifiedCarType: !!specifiedCarType
        });

        // 如果有人数信息，根据人数进行车型选择
        if (passengerNumber > 0) {
            if (passengerNumber >= 30) {
                // 超大型团队 - 30座以上
                selectedCarType = carTypes.find(car => car.seat_number >= 30);
                selectionReason = `超大型团队${passengerNumber}人，选择${selectedCarType?.seat_number || 30}座车辆`;
            } else if (passengerNumber >= 16) {
                // 大型团队 - 16-29人
                selectedCarType = carTypes.find(car => car.seat_number >= 16 && car.seat_number < 30);
                if (!selectedCarType) {
                    selectedCarType = carTypes.find(car => car.seat_number >= 16);
                }
                selectionReason = `大型团队${passengerNumber}人，选择${selectedCarType?.seat_number || 16}座车辆`;
            } else if (passengerNumber >= 12) {
                // 中大型团队 - 12-15人
                selectedCarType = carTypes.find(car => car.seat_number >= 12 && car.seat_number < 16);
                if (!selectedCarType) {
                    selectedCarType = carTypes.find(car => car.seat_number >= 12);
                }
                selectionReason = `中大型团队${passengerNumber}人，选择${selectedCarType?.seat_number || 12}座车辆`;
            } else if (passengerNumber >= 9) {
                // 中型团队 - 9-11人
                selectedCarType = carTypes.find(car => car.seat_number >= 9 && car.seat_number < 12);
                if (!selectedCarType) {
                    selectedCarType = carTypes.find(car => car.seat_number >= 9);
                }
                selectionReason = `中型团队${passengerNumber}人，选择${selectedCarType?.seat_number || 9}座车辆`;
            } else if (passengerNumber >= 7) {
                // 小型团队 - 7-8人
                selectedCarType = carTypes.find(car => car.seat_number >= 7 && car.seat_number < 9);
                if (!selectedCarType) {
                    selectedCarType = carTypes.find(car => car.seat_number >= 7);
                }
                selectionReason = `小型团队${passengerNumber}人，选择${selectedCarType?.seat_number || 7}座车辆`;
            } else if (passengerNumber >= 5 || luggageNumber >= 4) {
                // 5-6人或行李较多 - 选择6座MPV
                selectedCarType = carTypes.find(car => car.seat_number === 6) ||
                                 carTypes.find(car => car.seat_number >= 6 && car.seat_number < 9);
                selectionReason = `${passengerNumber}人${luggageNumber}件行李，选择6座MPV`;
            } else if (passengerNumber >= 3 || luggageNumber >= 2) {
                // 3-4人 - 选择舒适型5座
                selectedCarType = carTypes.find(car => car.type?.includes('Comfort') && car.seat_number === 4) ||
                                 carTypes.find(car => car.seat_number === 4 && car.priority === 2);
                selectionReason = `${passengerNumber}人${luggageNumber}件行李，选择舒适型5座车`;
            } else {
                // 1-2人 - 选择紧凑型5座
                selectedCarType = carTypes.find(car => car.type?.includes('Compact') && car.seat_number === 4) ||
                                 carTypes.find(car => car.seat_number === 4 && car.priority === 1);
                selectionReason = `${passengerNumber}人${luggageNumber}件行李，选择紧凑型5座车`;
            }
        } else {
            // 没有人数信息，使用默认车型：Comfort 5 Seater (ID: 6)
            selectedCarType = carTypes.find(car => car.type?.includes('Comfort') && car.seat_number === 4) ||
                             carTypes.find(car => car.id === 6) ||
                             carTypes.find(car => car.seat_number === 4 && car.priority === 2);
            selectionReason = '未提供人数信息，使用默认车型：Comfort 5 Seater';
        }

        // 如果没有找到合适的车型，使用默认的 Comfort 5 Seater
        if (!selectedCarType) {
            selectedCarType = carTypes.find(car => car.type?.includes('Comfort') && car.seat_number === 4) ||
                             carTypes.find(car => car.id === 6) ||
                             carTypes.sort((a, b) => a.priority - b.priority)[0];
            selectionReason = '未找到完全匹配的车型，使用默认车型：Comfort 5 Seater';
        }

        logger.success('智能选择', '车型选择完成', {
            selectedCarTypeId: selectedCarType.id,
            selectedCarTypeName: selectedCarType.type,
            selectedSeatNumber: selectedCarType.seat_number,
            selectedPriority: selectedCarType.priority,
            passengerNumber: passengerNumber,
            luggageNumber: luggageNumber,
            selectionReason: selectionReason
        });

        return selectedCarType.id;
    }

    /**
     * @function getSelectionSummary - 获取选择摘要
     * @param {object} order - 订单对象
     * @returns {object} 选择摘要
     */
    getSelectionSummary(order) {
        const backendUserId = this.selectBackendUser(order);
        const subCategoryId = this.selectSubCategory(order);
        const carTypeId = this.selectCarType(order);

        const summary = {
            backendUserId: backendUserId,
            subCategoryId: subCategoryId,
            carTypeId: carTypeId,
            backendUserName: this.appState.backendUsers.find(u => u.id === backendUserId)?.name || 'Unknown',
            subCategoryName: this.appState.subCategories.find(c => c.id === subCategoryId)?.name || 'Unknown',
            carTypeName: this.appState.carTypes.find(c => c.id === carTypeId)?.type || 'Unknown'
        };

        logger.info('智能选择', '选择摘要', summary);
        return summary;
    }
}

// 订单处理器
class OrderProcessor {
    constructor(apiService) {
        this.apiService = apiService;
    }

    /**
     * @function createMeetAndGreetOrder - 创建举牌服务订单
     * @param {object} originalOrder - 原始订单对象
     * @param {object} meetAndGreetInfo - 举牌服务信息
     * @returns {object} 举牌服务订单数据
     */
    createMeetAndGreetOrder(originalOrder, meetAndGreetInfo) {
        logger.info('订单处理', '创建举牌服务订单', {
            originalOrderId: originalOrder.id || 'unknown',
            customerName: meetAndGreetInfo.customerName
        });

        // 基于原订单创建举牌服务订单
        const meetAndGreetOrder = {
            id: `${originalOrder.id || Date.now()}_meet_greet`,
            type: 'meet_and_greet',
            parent_order_id: originalOrder.id,
            ota_type: originalOrder.ota_type,
            original_content: originalOrder.original_content,
            parsed: {
                ...originalOrder.parsed,
                // 举牌服务特定字段
                '服务类型': '举牌服务',
                'meet_and_greet': meetAndGreetInfo.customerName || originalOrder.parsed['姓名'] || originalOrder.parsed['客人姓名'] || originalOrder.parsed['联系人'],
                '备注': `举牌服务 - ${meetAndGreetInfo.customerName || '客人姓名'}`,
                '价格': 0, // 举牌服务通常免费或单独计费
                '司机费': 0
            }
        };

        // 如果是接机订单，举牌服务使用相同的时间和地点
        if (originalOrder.parsed['服务类型'] === '接机' || originalOrder.parsed['pickup'] === 'klia') {
            meetAndGreetOrder.parsed['日期'] = originalOrder.parsed['日期'];
            meetAndGreetOrder.parsed['时间'] = originalOrder.parsed['时间'];
            meetAndGreetOrder.parsed['pickup'] = originalOrder.parsed['pickup'] || 'klia';
            meetAndGreetOrder.parsed['drop'] = originalOrder.parsed['drop'] || originalOrder.parsed['destination'];
        }

        // 生成举牌服务专用的OTA参考号
        meetAndGreetOrder.parsed['OTA参考号'] = this.generateMeetAndGreetReference(originalOrder, meetAndGreetInfo);

        logger.success('订单处理', '举牌服务订单创建完成', {
            meetAndGreetOrderId: meetAndGreetOrder.id,
            customerName: meetAndGreetOrder.parsed['meet_and_greet']
        });

        return meetAndGreetOrder;
    }

    /**
     * @function generateMeetAndGreetReference - 生成举牌服务参考号
     * @param {object} originalOrder - 原始订单
     * @param {object} meetAndGreetInfo - 举牌服务信息
     * @returns {string} 举牌服务参考号
     */
    generateMeetAndGreetReference(originalOrder, meetAndGreetInfo) {
        const originalRef = originalOrder.parsed['OTA参考号'] || '';
        const customerName = meetAndGreetInfo.customerName || originalOrder.parsed['姓名'] || 'Guest';

        // 在原参考号基础上添加举牌服务标识
        const meetGreetRef = originalRef ? `${originalRef}-MEET` : `${Date.now()}-${customerName}-MEET`;

        logger.debug('订单处理', '生成举牌服务参考号', {
            originalRef: originalRef,
            meetGreetRef: meetGreetRef
        });

        return meetGreetRef;
    }

    /**
     * @function processOrdersWithMeetAndGreet - 处理包含举牌服务的订单
     * @param {Array} orders - 订单列表
     * @param {object} meetAndGreetInfo - 举牌服务信息
     * @returns {Array} 处理后的订单列表（包含举牌服务订单）
     */
    processOrdersWithMeetAndGreet(orders, meetAndGreetInfo) {
        logger.info('订单处理', '处理举牌服务订单集成', {
            originalOrderCount: orders.length,
            needsMeetAndGreet: meetAndGreetInfo.needsMeetAndGreet
        });

        if (!meetAndGreetInfo.needsMeetAndGreet) {
            return orders;
        }

        const processedOrders = [...orders];

        // 为每个接机订单创建对应的举牌服务订单
        orders.forEach(order => {
            const isPickupOrder = order.parsed['服务类型'] === '接机' ||
                                 order.parsed['pickup'] === 'klia' ||
                                 order.parsed['航班'];

            if (isPickupOrder) {
                const meetAndGreetOrder = this.createMeetAndGreetOrder(order, meetAndGreetInfo);
                processedOrders.push(meetAndGreetOrder);

                logger.info('订单处理', '为接机订单添加举牌服务', {
                    originalOrderId: order.id,
                    meetAndGreetOrderId: meetAndGreetOrder.id
                });
            }
        });

        logger.success('订单处理', '举牌服务订单集成完成', {
            finalOrderCount: processedOrders.length,
            addedMeetAndGreetOrders: processedOrders.length - orders.length
        });

        return processedOrders;
    }
}

// 主应用类
class OTAOrderApp {
    constructor() {
        logger.info('应用', 'OTAOrderApp 构造函数开始执行');
        try {
            this.appState = new AppState();
            this.apiService = new ApiService(this.appState);
            this.llmService = new LLMService(); // 更新为 LLMService
            this.imageService = new ImageService();
            this.smartSelectionService = new SmartSelectionService(this.appState);
            this.orderProcessor = new OrderProcessor(this.apiService);

            // LLM 状态检测相关变量
            this.llmCheckInterval = null;
            this.llmCheckTimeout = null;

            this.initializeApp();
            logger.success('应用', 'OTAOrderApp 构造完成');
        } catch (error) {
            logger.error('应用', 'OTAOrderApp 构造失败', error);
            throw error;
        }
    }

    /**
     * @function initializeApp - 初始化应用
     */
    initializeApp() {
        logger.info('应用', '开始初始化应用');
        try {
            // 绑定事件监听器
            logger.debug('初始化', '绑定事件监听器');
            this.bindEvents();

            // 检查认证状态
            logger.debug('初始化', '检查认证状态');
            this.checkAuthStatus();

            // 初始化 LLM 状态指示器
            logger.debug('初始化', '初始化 LLM 状态指示器');
            this.initializeLLMStatusIndicator();

            logger.success('应用', '应用初始化完成');
        } catch (error) {
            logger.error('应用', '应用初始化失败', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    }

    /**
     * @function initializeLLMStatusIndicator - 初始化 LLM 状态指示器
     */
    initializeLLMStatusIndicator() {
        // 绑定 DeepSeek 状态指示器点击事件
        const deepseekIndicator = document.getElementById('deepseekStatusIndicator');
        if (deepseekIndicator) {
            deepseekIndicator.addEventListener('click', () => {
                this.checkDeepSeekConnection(true); // 手动检测
            });
        }

        // 绑定 Gemini 状态指示器点击事件
        const geminiIndicator = document.getElementById('geminiStatusIndicator');
        if (geminiIndicator) {
            geminiIndicator.addEventListener('click', () => {
                this.checkGeminiConnection(true); // 手动检测
            });
        }

        // 初始检测连接状态
        this.checkDeepSeekConnection();
        this.checkGeminiConnection();

        // 启动智能自动检测
        this.startSmartAutoDetection();
    }

    /**
     * @function startSmartAutoDetection - 启动智能自动检测
     */
    startSmartAutoDetection() {
        // 清除可能存在的旧定时器
        if (this.llmCheckInterval) {
            clearInterval(this.llmCheckInterval);
        }

        // 设置智能检测间隔
        const scheduleNextCheck = () => {
            const deepseekStatus = this.llmService.getDeepSeekStatus();
            const geminiStatus = this.llmService.getGeminiStatus();

            let interval;

            // 根据连接状态和失败次数动态调整检测间隔
            const maxFailures = Math.max(deepseekStatus.consecutiveFailures, geminiStatus.consecutiveFailures);
            const hasConnected = deepseekStatus.status === 'connected' || geminiStatus.status === 'connected';

            if (hasConnected) {
                // 有连接正常时，5分钟检测一次
                interval = 5 * 60 * 1000;
            } else if (maxFailures <= 2) {
                // 初次失败，1分钟后重试
                interval = 1 * 60 * 1000;
            } else if (maxFailures <= 5) {
                // 多次失败，2分钟后重试
                interval = 2 * 60 * 1000;
            } else {
                // 持续失败，5分钟后重试
                interval = 5 * 60 * 1000;
            }

            logger.debug('LLM', '计划下次自动检测', {
                deepseekStatus: deepseekStatus.status,
                geminiStatus: geminiStatus.status,
                maxFailures: maxFailures,
                nextCheckIn: `${interval / 1000}秒`
            });

            this.llmCheckTimeout = setTimeout(async () => {
                await this.checkDeepSeekConnection();
                await this.checkGeminiConnection();
                scheduleNextCheck(); // 递归调度下次检测
            }, interval);
        };

        // 开始调度
        scheduleNextCheck();
    }

    /**
     * @function checkDeepSeekConnection - 检测 DeepSeek 连接状态
     * @param {boolean} isManual - 是否为手动检测
     */
    async checkDeepSeekConnection(isManual = false) {
        const previousStatus = this.llmService.getDeepSeekStatus();

        logger.debug('DeepSeek', '开始检测 DeepSeek 连接状态', {
            isManual: isManual,
            previousStatus: previousStatus.status
        });

        // 更新状态为检测中
        this.updateDeepSeekStatusIndicator('checking', isManual ? 'DeepSeek 手动检测中...' : 'DeepSeek 自动检测中...');

        try {
            const isConnected = await this.llmService.checkDeepSeekConnection();
            const status = this.llmService.getDeepSeekStatus();

            // 检测状态是否发生变化
            const statusChanged = previousStatus.status !== status.status;

            this.updateDeepSeekStatusIndicator(status.status, status.text);

            // 如果状态发生变化，提供用户通知
            if (statusChanged && !isManual) {
                this.notifyLLMStatusChange('DeepSeek', previousStatus.status, status.status);
            }

            logger.info('DeepSeek', `连接状态检测完成: ${status.text}`, {
                status: status.status,
                connected: isConnected,
                statusChanged: statusChanged,
                isManual: isManual,
                consecutiveFailures: status.consecutiveFailures
            });

            return isConnected;
        } catch (error) {
            logger.error('DeepSeek', '连接状态检测失败', error);
            this.updateDeepSeekStatusIndicator('disconnected', 'DeepSeek 连接失败');

            // 如果是手动检测失败，显示错误提示
            if (isManual) {
                this.showLLMError('DeepSeek 手动检测失败，请检查网络连接和API配置');
            }

            return false;
        }
    }

    /**
     * @function checkGeminiConnection - 检测 Gemini 连接状态
     * @param {boolean} isManual - 是否为手动检测
     */
    async checkGeminiConnection(isManual = false) {
        const previousStatus = this.llmService.getGeminiStatus();

        logger.debug('Gemini', '开始检测 Gemini 连接状态', {
            isManual: isManual,
            previousStatus: previousStatus.status
        });

        // 更新状态为检测中
        this.updateGeminiStatusIndicator('checking', isManual ? 'Gemini 手动检测中...' : 'Gemini 自动检测中...');

        try {
            const isConnected = await this.llmService.checkGeminiConnection();
            const status = this.llmService.getGeminiStatus();

            // 检测状态是否发生变化
            const statusChanged = previousStatus.status !== status.status;

            this.updateGeminiStatusIndicator(status.status, status.text);

            // 如果状态发生变化，提供用户通知
            if (statusChanged && !isManual) {
                this.notifyLLMStatusChange('Gemini', previousStatus.status, status.status);
            }

            logger.info('Gemini', `连接状态检测完成: ${status.text}`, {
                status: status.status,
                connected: isConnected,
                statusChanged: statusChanged,
                isManual: isManual,
                consecutiveFailures: status.consecutiveFailures
            });

            return isConnected;
        } catch (error) {
            logger.error('Gemini', '连接状态检测失败', error);
            this.updateGeminiStatusIndicator('disconnected', 'Gemini 连接失败');

            // 如果是手动检测失败，显示错误提示
            if (isManual) {
                this.showLLMError('Gemini 手动检测失败，请检查网络连接和API配置');
            }

            return false;
        }
    }

    /**
     * @function notifyGeminiStatusChange - 通知 Gemini 状态变化
     * @param {string} previousStatus - 之前的状态
     * @param {string} currentStatus - 当前状态
     */
    notifyGeminiStatusChange(previousStatus, currentStatus) {
        if (previousStatus === 'disconnected' && currentStatus === 'connected') {
            // 从断开恢复到连接
            logger.success('Gemini', '🎉 Gemini API 连接已恢复，AI 功能现在可用');
            this.showGeminiNotification('Gemini 连接已恢复', 'success');
        } else if (previousStatus === 'connected' && currentStatus === 'disconnected') {
            // 从连接变为断开
            logger.warn('Gemini', '⚠️ Gemini API 连接中断，AI 功能暂时不可用');
            this.showGeminiNotification('Gemini 连接中断', 'warning');
        }
    }

    /**
     * @function showGeminiNotification - 显示 Gemini 状态通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知类型 (success/warning/error)
     */
    showGeminiNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `gemini-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '❌'}</span>
                <span class="notification-message">${message}</span>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' : type === 'warning' ? '#fff3cd' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : type === 'warning' ? '#856404' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'warning' ? '#ffeaa7' : '#f5c6cb'};
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }

    /**
     * @function showGeminiError - 显示 Gemini 错误提示
     * @param {string} message - 错误消息
     */
    showGeminiError(message) {
        this.showGeminiNotification(message, 'error');
    }

    /**
     * @function updateGeminiStatusIndicator - 更新 Gemini 状态指示器
     * @param {string} status - 状态（checking/connected/disconnected）
     * @param {string} text - 状态文字
     */
    updateGeminiStatusIndicator(status, text) {
        const statusLight = document.getElementById('geminiStatusLight');
        const statusText = document.getElementById('geminiStatusText');

        if (statusLight && statusText) {
            // 清除所有状态类
            statusLight.classList.remove('checking', 'connected', 'disconnected');

            // 添加当前状态类
            statusLight.classList.add(status);

            // 更新状态文字
            statusText.textContent = text;

            logger.debug('UI', '更新 Gemini 状态指示器', {
                status: status,
                text: text
            });
        }
    }

    /**
     * @function checkAuthStatus - 检查认证状态
     */
    checkAuthStatus() {
        if (this.appState.token) {
            this.showMainApp();
            this.loadSystemData();
        } else {
            this.showLoginModal();
        }
    }

    /**
     * @function bindEvents - 绑定事件监听器
     */
    bindEvents() {
        // 登录表单
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 图片分析标签页切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('analysis-tab-btn')) {
                this.switchAnalysisTab(e.target.dataset.analysisTab);
            }
        });

        // 图片上传
        const uploadArea = document.getElementById('uploadArea');
        const imageFile = document.getElementById('imageFile');
        
        uploadArea.addEventListener('click', () => imageFile.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        imageFile.addEventListener('change', this.handleImageSelect.bind(this));

        // 处理订单
        document.getElementById('processBtn').addEventListener('click', () => {
            this.handleProcessOrder();
        });

        // 编辑结果
        document.getElementById('editBtn').addEventListener('click', () => {
            this.enableResultEditing();
        });

        // 重新处理
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.handleProcessOrder();
        });

        // 创建订单
        document.getElementById('createOrderBtn').addEventListener('click', () => {
            this.handleCreateOrders();
        });

        // 导出结果
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportResults();
        });
    }

    /**
     * @function handleLogin - 处理登录
     */
    async handleLogin() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');
        
        logger.info('认证', '开始登录流程', { email });
        
        if (!email || !password) {
            logger.warn('认证', '登录参数不完整');
            this.showError('请输入邮箱和密码');
            return;
        }
        
        try {
            this.showLoading('正在登录...');
            logger.debug('认证', '发送登录请求');
            
            const result = await this.apiService.login(email, password);
            logger.debug('认证', '收到登录响应', result);
            
            if (result.success) {
                logger.success('认证', '登录成功');
                this.hideLoading();
                this.showMainApp();
                this.loadSystemData();
            }
        } catch (error) {
            logger.error('认证', '登录过程发生错误', error);
            this.hideLoading();
            errorDiv.textContent = error.message;
        } finally {
            logger.debug('认证', '登录流程结束');
        }
    }

    /**
     * @function handleLogout - 处理退出登录
     */
    handleLogout() {
        this.appState.clearAuth();
        this.showLoginModal();
    }

    /**
     * @function loadSystemData - 加载系统数据
     */
    async loadSystemData() {
        try {
            this.showLoading('正在加载系统数据...');
            
            // 并行加载所有系统数据
            await Promise.all([
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes()
            ]);
            
            this.hideLoading();
            this.updateUserInfo();
        } catch (error) {
            this.hideLoading();
            console.error('加载系统数据失败:', error);
        }
    }

    /**
     * @function updateUserInfo - 更新用户信息显示
     */
    updateUserInfo() {
        const userInfoElement = document.getElementById('userInfo');
        userInfoElement.textContent = `已登录 | 后台用户: ${this.appState.backendUsers.length} | 子分类: ${this.appState.subCategories.length} | 车型: ${this.appState.carTypes.length}`;
    }

    /**
     * @function switchTab - 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Input`).classList.add('active');
    }

    /**
     * @function switchAnalysisTab - 切换图片分析标签页
     * @param {string} tabName - 标签页名称
     */
    switchAnalysisTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.analysis-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-analysis-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容显示
        document.querySelectorAll('.analysis-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName === 'text' ? 'extractedText' : 'analysisDetails').classList.add('active');
    }

    /**
     * @function showImageAnalysisResult - 显示图片分析结果
     * @param {Object} analysisResult - 分析结果
     */
    showImageAnalysisResult(analysisResult) {
        const resultContainer = document.getElementById('imageAnalysisResult');
        const extractedTextArea = document.getElementById('extractedTextContent');
        const labelsList = document.getElementById('labelsList');
        const objectsList = document.getElementById('objectsList');

        // 显示提取的文字
        extractedTextArea.value = analysisResult.text || '未检测到文字内容';

        // 显示标签
        labelsList.innerHTML = '';
        if (analysisResult.labels && analysisResult.labels.length > 0) {
            analysisResult.labels.forEach(label => {
                const labelTag = document.createElement('div');
                labelTag.className = 'label-tag';
                labelTag.innerHTML = `
                    ${label.description}
                    <span class="confidence-score">${(label.score * 100).toFixed(1)}%</span>
                `;
                labelsList.appendChild(labelTag);
            });
        } else {
            labelsList.innerHTML = '<p>未检测到相关标签</p>';
        }

        // 显示物体
        objectsList.innerHTML = '';
        if (analysisResult.objects && analysisResult.objects.length > 0) {
            analysisResult.objects.forEach(obj => {
                const objectTag = document.createElement('div');
                objectTag.className = 'object-tag';
                objectTag.innerHTML = `
                    ${obj.name}
                    <span class="confidence-score">${(obj.score * 100).toFixed(1)}%</span>
                `;
                objectsList.appendChild(objectTag);
            });
        } else {
            objectsList.innerHTML = '<p>未检测到特定物体</p>';
        }

        // 显示分析结果容器
        resultContainer.classList.remove('hidden');

        logger.success('图片分析', '分析结果显示完成', {
            textLength: analysisResult.text?.length || 0,
            labelsCount: analysisResult.labels?.length || 0,
            objectsCount: analysisResult.objects?.length || 0
        });
    }

    /**
     * @function hideImageAnalysisResult - 隐藏图片分析结果
     */
    hideImageAnalysisResult() {
        document.getElementById('imageAnalysisResult').classList.add('hidden');
    }

    /**
     * @function handleDragOver - 处理拖拽悬停
     * @param {Event} e - 事件对象
     */
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    /**
     * @function handleDrop - 处理文件拖拽放置
     * @param {Event} e - 事件对象
     */
    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files).filter(file => 
            file.type.startsWith('image/')
        );
        
        this.processImages(files);
    }

    /**
     * @function handleImageSelect - 处理图片选择
     * @param {Event} e - 事件对象
     */
    handleImageSelect(e) {
        const files = Array.from(e.target.files);
        this.processImages(files);
    }

    /**
     * @function processImages - 处理图片文件
     * @param {File[]} files - 图片文件数组
     */
    async processImages(files) {
        const previewContainer = document.getElementById('imagePreview');
        const imageFileInput = document.getElementById('imageFile');
        
        // 验证并过滤图片文件
        const validFiles = [];
        
        // 隐藏之前的分析结果
        this.hideImageAnalysisResult();
        
        files.forEach((file, index) => {
            // 验证文件
            const validation = this.imageService.validateImageFile(file);
            
            if (validation.isValid) {
                validFiles.push(file);
                
                // 创建预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';
                    imageItem.dataset.fileIndex = validFiles.length - 1;
                    imageItem.innerHTML = `
                        <img src="${e.target.result}" alt="上传的图片">
                        <div class="image-info">
                            <span class="file-name">${file.name}</span>
                            <span class="file-size">${(file.size / 1024 / 1024).toFixed(2)}MB</span>
                        </div>
                        <button class="image-remove" onclick="this.parentElement.remove()">×</button>
                    `;
                    previewContainer.appendChild(imageItem);
                };
                reader.readAsDataURL(file);
                
                logger.info('图片上传', '图片验证通过', {
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                });
            } else {
                logger.error('图片上传', '图片验证失败', {
                    fileName: file.name,
                    errors: validation.errors
                });
                
                // 显示错误信息
                this.showError(`文件 "${file.name}" 上传失败: ${validation.errors.join(', ')}`);
            }
        });

        // 更新文件输入的files属性以保存有效文件
        if (validFiles.length > 0) {
            // 创建新的DataTransfer对象来设置files
            const dt = new DataTransfer();
            validFiles.forEach(file => dt.items.add(file));
            imageFileInput.files = dt.files;
            
            logger.success('图片上传', '图片处理完成', {
                totalFiles: files.length,
                validFiles: validFiles.length
            });

            // 自动分析第一张图片（可选功能）
            if (validFiles.length > 0) {
                try {
                    logger.info('图片分析', '开始自动分析第一张图片');
                    this.showLoading('正在分析图片...');
                    
                    const analysisResult = await this.imageService.analyzeImageContent(validFiles[0]);
                    this.showImageAnalysisResult(analysisResult);
                    
                    this.hideLoading();
                    logger.success('图片分析', '自动分析完成');
                } catch (error) {
                    this.hideLoading();
                    logger.warn('图片分析', '自动分析失败', {
                        error: error.message
                    });
                    // 分析失败不影响上传，只是不显示分析结果
                }
            }
        }
    }

    /**
     * @function handleProcessOrder - 处理订单
     */
    async handleProcessOrder() {
        logger.info('订单处理', '开始处理订单流程');
        
        try {
            this.showLoading('正在处理订单...');
            
            // 获取订单内容
            let orderContent = '';
            const activeTab = document.querySelector('.tab-btn.active').dataset.tab;
            
            logger.debug('订单处理', '获取订单内容', {
                activeTab: activeTab
            });
            
            if (activeTab === 'text') {
                orderContent = document.getElementById('orderText').value.trim();
                logger.debug('订单处理', '文本输入模式', {
                    contentLength: orderContent.length
                });
            } else if (activeTab === 'image') {
                // 从图片中提取文字使用Google Vision API
                const images = document.querySelectorAll('#imagePreview img');
                logger.debug('订单处理', '图片输入模式', {
                    imageCount: images.length
                });
                
                if (images.length === 0) {
                    logger.error('订单处理', '图片输入模式但未上传图片');
                    throw new Error('请先上传图片');
                }
                
                // 获取所有上传的图片文件
                const imageFiles = Array.from(document.getElementById('imageFile').files);
                if (imageFiles.length === 0) {
                    logger.error('订单处理', '无法获取图片文件');
                    throw new Error('无法获取图片文件，请重新上传');
                }

                logger.info('订单处理', '开始从图片提取文字', {
                    fileCount: imageFiles.length
                });

                this.updateLoadingText('正在分析图片...');

                // 处理所有图片
                const extractedTexts = [];
                for (let i = 0; i < imageFiles.length; i++) {
                    const file = imageFiles[i];
                    
                    logger.debug('订单处理', `处理第 ${i + 1} 张图片`, {
                        fileName: file.name,
                        fileSize: file.size,
                        fileType: file.type
                    });

                    // 验证图片文件
                    const validation = this.imageService.validateImageFile(file);
                    if (!validation.isValid) {
                        logger.error('订单处理', '图片文件验证失败', {
                            fileName: file.name,
                            errors: validation.errors
                        });
                        throw new Error(`图片 "${file.name}" 验证失败: ${validation.errors.join(', ')}`);
                    }

                    this.updateLoadingText(`正在分析第 ${i + 1}/${imageFiles.length} 张图片...`);

                    try {
                        // 使用Google Vision API提取文字
                        const extractedText = await this.imageService.extractTextFromImage(file);
                        
                        if (extractedText && extractedText.trim()) {
                            extractedTexts.push(`=== 图片 ${i + 1} (${file.name}) ===\n${extractedText.trim()}`);
                            logger.success('订单处理', `第 ${i + 1} 张图片文字提取成功`, {
                                fileName: file.name,
                                textLength: extractedText.length
                            });
                        } else {
                            logger.warn('订单处理', `第 ${i + 1} 张图片未检测到文字`, {
                                fileName: file.name
                            });
                        }
                    } catch (error) {
                        logger.error('订单处理', `第 ${i + 1} 张图片处理失败`, {
                            fileName: file.name,
                            error: error.message
                        });
                        // 继续处理其他图片，但记录错误
                        extractedTexts.push(`=== 图片 ${i + 1} (${file.name}) ===\n[图片处理失败: ${error.message}]`);
                    }
                }

                // 合并所有提取的文字
                orderContent = extractedTexts.join('\n\n');
                
                if (!orderContent.trim()) {
                    logger.error('订单处理', '所有图片都未能提取到有效文字');
                    throw new Error('无法从图片中提取到有效的文字内容，请确保图片清晰且包含文字信息');
                }

                logger.success('订单处理', '图片文字提取完成', {
                    totalImages: imageFiles.length,
                    successCount: extractedTexts.filter(text => !text.includes('[图片处理失败')).length,
                    totalTextLength: orderContent.length
                });

                this.updateLoadingText('正在处理提取的内容...');
            }
            
            if (!orderContent) {
                logger.error('订单处理', '订单内容为空');
                throw new Error('请输入订单内容');
            }
            
            logger.info('订单处理', '订单内容获取成功', {
                contentLength: orderContent.length,
                inputMode: activeTab
            });
            
            // 获取OTA类型
        let otaType = document.getElementById('otaSelect').value;
        
        logger.debug('订单处理', '获取OTA类型设置', {
            selectedOtaType: otaType
        });
        
        // 如果选择了自动识别，先进行本地OTA类型检测
        if (otaType === 'auto') {
            logger.info('订单处理', '开始自动OTA类型识别');

            // 第一步：本地特征识别
            const localDetection = this.geminiService.detectOTATypeLocally(orderContent);
            logger.debug('订单处理', '本地OTA类型识别结果', localDetection);

            // 如果本地识别置信度足够高，直接使用
            if (localDetection.confidence >= 0.6) {
                otaType = localDetection.ota_type;
                logger.success('订单处理', '本地OTA识别置信度足够，直接使用', {
                    otaType: otaType,
                    confidence: localDetection.confidence,
                    reasoning: localDetection.reasoning
                });
            } else {
                // 第二步：本地识别置信度不够，使用Gemini AI进行深度识别
                logger.info('订单处理', '本地识别置信度不够，启用AI深度识别', {
                    localConfidence: localDetection.confidence
                });

                try {
                    const aiDetection = await this.geminiService.detectOTAType(orderContent);
                    logger.debug('订单处理', 'AI OTA类型识别结果', aiDetection);
                    
                    // 综合本地识别和AI识别结果
                    if (aiDetection.confidence > 0.7) {
                        otaType = aiDetection.ota_type;
                        logger.success('订单处理', 'AI识别置信度高，使用AI结果', {
                            otaType: otaType,
                            aiConfidence: aiDetection.confidence
                        });

                    } else if (localDetection.confidence > 0.3) {
                        // AI识别也不确定，但本地有一定置信度，使用本地结果
                        otaType = localDetection.ota_type;
                        logger.warn('订单处理', 'AI识别不确定，回退到本地识别结果', {
                            localOtaType: localDetection.ota_type,
                            localConfidence: localDetection.confidence,
                            aiConfidence: aiDetection.confidence
                        });

                    } else {
                        // 都不确定，使用通用模式
                        logger.warn('订单处理', '本地和AI识别都不确定，使用通用模式', {
                            localConfidence: localDetection.confidence,
                            aiConfidence: aiDetection.confidence
                        });

                        otaType = 'auto';
                    }
                } catch (error) {
                    logger.error('订单处理', 'AI OTA类型识别失败', {
                        error: error.message,
                        localConfidence: localDetection.confidence
                    });
                    if (localDetection.confidence > 0.3) {
                        otaType = localDetection.ota_type;
                        logger.warn('订单处理', 'AI识别失败，回退到本地识别', {
                            otaType: otaType
                        });

                    } else {
                        logger.warn('订单处理', 'AI和本地识别都失败，使用通用模式');

                        otaType = 'auto';
                    }
                }
            }
        }
        
        // 使用AI处理订单
        logger.info('订单处理', '开始AI订单处理', {
            finalOtaType: otaType,
            contentLength: orderContent.length
        });

        const processedResult = await this.geminiService.processOrderWithAI(orderContent, otaType);

            // 解析处理结果，传递实际的OTA类型
            logger.debug('订单处理', '开始解析AI处理结果', {
                resultLength: processedResult?.length || 0,
                otaType: otaType
            });
            this.appState.processedOrders = this.parseProcessedOrders(processedResult, otaType);

            // 检测举牌服务需求
            logger.info('订单处理', '开始举牌服务检测');

            const meetAndGreetInfo = this.geminiService.detectMeetAndGreetService(orderContent);

            if (meetAndGreetInfo.needsMeetAndGreet) {
                logger.success('订单处理', '检测到举牌服务需求，正在生成举牌服务订单');

                // 处理举牌服务订单
                this.appState.processedOrders = this.orderProcessor.processOrdersWithMeetAndGreet(
                    this.appState.processedOrders,
                    meetAndGreetInfo
                );
            } else {
                logger.debug('订单处理', '未检测到举牌服务需求');

            }
            
            logger.success('订单处理', '订单处理流程完成', {
                processedOrdersCount: this.appState.processedOrders?.length || 0
            });
            
            this.hideLoading();
            this.showProcessedResults(processedResult);
            
        } catch (error) {
            logger.error('订单处理', '订单处理流程失败', {
                error: error.message,
                stack: error.stack
            });
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * @function parseProcessedOrders - 解析处理后的订单
     * @param {string} processedText - 处理后的文本
     * @param {string} actualOtaType - 实际的OTA类型
     * @returns {array} 解析后的订单数组
     */
    parseProcessedOrders(processedText, actualOtaType = null) {
        // 简单的解析逻辑，实际应该更复杂
        const orders = [];
        const orderBlocks = processedText.split('\n\n').filter(block => block.trim());
        
        // 获取OTA类型，优先使用传入的实际类型
        const otaType = actualOtaType || document.getElementById('otaSelect').value;
        
        orderBlocks.forEach((block, index) => {
            const lines = block.split('\n');
            const order = {
                id: index + 1,
                raw: block,
                parsed: {},
                ota_type: this.mapOtaType(otaType) // 设置OTA类型
            };
            
            lines.forEach(line => {
                const [key, value] = line.split(':').map(s => s.trim());
                if (key && value) {
                    order.parsed[key] = value;
                }
            });
            
            orders.push(order);
        });
        
        return orders;
    }

    /**
     * @function mapOtaType - 映射OTA类型
     * @param {string} selectedType - 选择的类型
     * @returns {string} 映射后的类型
     */
    mapOtaType(selectedType) {
        switch (selectedType) {
            case 'chong-dealer':
                return 'Chong Dealer';
            case 'auto':
                // 对于自动识别，需要从实际识别结果中获取
                // 这里暂时返回通用类型，实际应该从识别结果中获取
                return 'Auto';
            default:
                return 'Other';
        }
    }

    /**
     * @function showProcessedResults - 显示处理结果
     * @param {string} results - 处理结果
     */
    showProcessedResults(results) {
        const resultContent = document.getElementById('resultContent');
        resultContent.textContent = results;
        
        document.getElementById('resultPreview').classList.remove('hidden');
        document.getElementById('orderStatus').classList.add('hidden');
    }

    /**
     * @function enableResultEditing - 启用结果编辑
     */
    enableResultEditing() {
        const resultContent = document.getElementById('resultContent');
        const currentContent = resultContent.textContent;
        
        resultContent.innerHTML = `<textarea style="width: 100%; min-height: 300px; font-family: 'Courier New', monospace;">${currentContent}</textarea>`;
        
        const textarea = resultContent.querySelector('textarea');
        textarea.addEventListener('blur', () => {
            resultContent.textContent = textarea.value;
            // 重新解析订单
            this.appState.processedOrders = this.parseProcessedOrders(textarea.value);
        });
    }

    /**
     * @function handleCreateOrders - 处理创建订单
     */
    async handleCreateOrders() {
        if (this.appState.processedOrders.length === 0) {
            this.showError('没有可创建的订单');
            return;
        }
        
        try {
            this.showLoading('正在创建订单...');
            
            const statusContent = document.getElementById('statusContent');
            statusContent.innerHTML = '';
            
            // 为每个订单创建API调用
            const createPromises = this.appState.processedOrders.map(async (order, index) => {
                try {
                    // 构建订单数据
                    const orderData = this.buildOrderData(order);
                    
                    // 调用创建订单API
                    const result = await this.apiService.createOrder(orderData);
                    
                    // 显示成功状态
                    this.addStatusItem(`订单 ${index + 1}`, '创建成功', 'success', result);
                    
                    return { success: true, order, result };
                } catch (error) {
                    // 显示失败状态
                    this.addStatusItem(`订单 ${index + 1}`, `创建失败: ${error.message}`, 'error');
                    return { success: false, order, error };
                }
            });
            
            await Promise.all(createPromises);
            
            this.hideLoading();
            document.getElementById('orderStatus').classList.remove('hidden');
            
        } catch (error) {
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * @function buildOrderData - 构建订单数据
     * @param {object} order - 订单对象
     * @returns {object} API所需的订单数据
     */
    buildOrderData(order) {
        logger.info('订单构建', '开始构建订单数据', {
            orderId: order.id,
            otaType: order.ota_type,
            hasSmartSelection: !!this.smartSelectionService
        });

        // 使用智能选择服务获取最佳参数
        const smartSelection = this.smartSelectionService.getSelectionSummary(order);

        // 根据解析的订单数据构建API所需格式
        const data = {
            sub_category_id: smartSelection.subCategoryId,
            car_type_id: smartSelection.carTypeId,
            incharge_by_backend_user_id: smartSelection.backendUserId,
            ota_reference_number: this.generateOTAReference(order),
            customer_name: order.parsed['姓名'] || order.parsed['客人姓名'] || order.parsed['联系人'] || '',
            customer_contact: order.parsed['电话'] || this.generateRandomContact(order),
            customer_email: order.parsed['邮箱'] || '<EMAIL>',
            flight_info: order.parsed['航班'] || '',
            pickup: order.parsed['pickup'] || order.parsed['上车地点'] || '',
            destination: order.parsed['drop'] || order.parsed['下车地点'] || '',
            date: order.parsed['日期'] || '',
            time: order.parsed['时间'] || '',
            passenger_number: order.parsed['人数'] || 1,
            luggage_number: order.parsed['行李数'] || 1,
            driver_fee: order.parsed['司机费'] || 1,
            ota_price: order.parsed['价格'] || 0,
            extra_requirement: order.parsed['备注'] || order.parsed['other'] || '⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单'
        };

        logger.debug('订单构建', '智能选择结果应用', {
            selectedBackendUser: `${smartSelection.backendUserName} (ID: ${smartSelection.backendUserId})`,
            selectedSubCategory: `${smartSelection.subCategoryName} (ID: ${smartSelection.subCategoryId})`,
            selectedCarType: `${smartSelection.carTypeName} (ID: ${smartSelection.carTypeId})`
        });
        
        // 根据OTA类型应用默认预设值
        if (order.ota_type === 'Chong Dealer') {
            data.ota = 'Chong Dealer';
            data.ota_price = 0;
            data.customer_email = '<EMAIL>';
            data.passenger_number = 1;
            data.luggage_number = 1;
            data.driver_fee = 1;
            data.extra_requirement = '⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单';

            logger.debug('订单构建', '应用Chong Dealer默认设置', {
                ota: data.ota,
                ota_price: data.ota_price,
                passenger_number: data.passenger_number,
                luggage_number: data.luggage_number,
                driver_fee: data.driver_fee
            });
        }

        logger.success('订单构建', '订单数据构建完成', {
            orderId: order.id,
            otaReferenceNumber: data.ota_reference_number,
            customerName: data.customer_name,
            serviceType: order.parsed?.['服务类型'],
            smartSelectionApplied: {
                backendUser: smartSelection.backendUserName,
                subCategory: smartSelection.subCategoryName,
                carType: smartSelection.carTypeName
            }
        });

        return data;
    }

    /**
     * @function getSubCategoryId - 获取子分类ID
     * @returns {number} 子分类ID
     */
    getSubCategoryId() {
        return this.appState.subCategories.length > 0 ? this.appState.subCategories[0].id : 1;
    }

    /**
     * @function getCarTypeId - 获取车型ID
     * @returns {number} 车型ID
     */
    getCarTypeId() {
        return this.appState.carTypes.length > 0 ? this.appState.carTypes[0].id : 1;
    }

    /**
     * @function getBackendUserId - 获取后台用户ID
     * @returns {number} 后台用户ID
     */
    getBackendUserId() {
        return this.appState.backendUsers.length > 0 ? this.appState.backendUsers[0].id : 1;
    }

    /**
     * @function generateOTAReference - 生成OTA参考号
     * @param {object} order - 订单对象
     * @returns {string} OTA参考号
     */
    generateOTAReference(order) {
        logger.debug('订单处理', '开始生成OTA参考号', {
            orderId: order.id,
            otaType: order.ota_type
        });

        // 获取订单信息
        const orderDate = order.parsed?.['日期'] || new Date().toISOString().slice(0, 10);
        const orderTime = order.parsed?.['时间'] || new Date().toTimeString().slice(0, 5);
        const flightInfo = order.parsed?.['航班'] || '';
        const customerName = order.parsed?.['姓名'] || order.parsed?.['客人姓名'] || order.parsed?.['联系人'] || '';

        // 格式化日期时间
        const dateFormatted = orderDate.replace(/-/g, '');
        const timeFormatted = orderTime.replace(':', '');

        let uniqueId;

        // 根据OTA类型生成不同格式的参考号
        if (order.ota_type === 'Chong Dealer') {
            // Chong Dealer 特殊格式：${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}-CHONG
            uniqueId = `${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}-CHONG`;
            logger.debug('订单处理', '使用Chong Dealer格式生成参考号', {
                format: 'CHONG格式',
                reference: uniqueId
            });
        } else {
            // 通用格式：${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}
            uniqueId = `${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}`;
            logger.debug('订单处理', '使用通用格式生成参考号', {
                format: '通用格式',
                reference: uniqueId
            });
        }

        // 检查重复并确保唯一性
        const finalReference = this.ensureUniqueReference(uniqueId, order);

        logger.success('订单处理', 'OTA参考号生成完成', {
            originalReference: uniqueId,
            finalReference: finalReference,
            isUnique: uniqueId === finalReference
        });

        return finalReference;
    }

    /**
     * @function ensureUniqueReference - 确保参考号唯一性
     * @param {string} baseReference - 基础参考号
     * @param {object} currentOrder - 当前订单
     * @returns {string} 唯一的参考号
     */
    ensureUniqueReference(baseReference, currentOrder) {
        // 检查当前已处理订单中是否有重复的参考号
        const existingReferences = this.appState.processedOrders
            .filter(order => order.id !== currentOrder.id) // 排除当前订单
            .map(order => order.parsed?.['OTA参考号']) // 只获取已存在的参考号，避免递归
            .filter(ref => ref); // 过滤空值

        let uniqueReference = baseReference;
        let counter = 1;

        // 如果存在重复，添加序号后缀
        while (existingReferences.includes(uniqueReference)) {
            uniqueReference = `${baseReference}-${counter.toString().padStart(2, '0')}`;
            counter++;

            // 防止无限循环
            if (counter > 99) {
                uniqueReference = `${baseReference}-${Date.now()}`;
                break;
            }
        }

        if (uniqueReference !== baseReference) {
            logger.warn('订单处理', '检测到参考号重复，已添加唯一后缀', {
                originalReference: baseReference,
                uniqueReference: uniqueReference,
                conflictCount: counter - 1
            });
        }

        return uniqueReference;
    }

    /**
     * @function generateRandomContact - 生成随机联系方式
     * @param {object} order - 订单对象
     * @returns {string} 随机联系方式
     */
    generateRandomContact(order) {
        // 基于订单信息生成随机联系方式
        const orderDate = order.parsed?.['日期'] || new Date().toISOString().slice(0, 10);
        const orderTime = order.parsed?.['时间'] || new Date().toTimeString().slice(0, 5);
        const flightInfo = order.parsed?.['航班'] || '';
        const customerName = order.parsed?.['姓名'] || order.parsed?.['客人姓名'] || order.parsed?.['联系人'] || '';
        
        // 格式化并生成联系方式
        const dateFormatted = orderDate.replace(/-/g, '');
        const timeFormatted = orderTime.replace(':', '');
        
        return `${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}`;
    }

    /**
     * @function addStatusItem - 添加状态项
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {string} type - 类型（success/error/pending）
     * @param {object} data - 附加数据
     */
    addStatusItem(title, message, type, data = null) {
        const statusContent = document.getElementById('statusContent');
        const statusItem = document.createElement('div');
        statusItem.className = `status-item ${type}`;
        
        let content = `<h4>${title}</h4><p>${message}</p>`;
        if (data) {
            content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }
        
        statusItem.innerHTML = content;
        statusContent.appendChild(statusItem);
    }

    /**
     * @function exportResults - 导出结果
     */
    exportResults() {
        const resultContent = document.getElementById('resultContent').textContent;
        const blob = new Blob([resultContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ota_orders_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * @function showLoginModal - 显示登录模态框
     */
    showLoginModal() {
        document.getElementById('loginModal').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
    }

    /**
     * @function showMainApp - 显示主应用
     */
    showMainApp() {
        document.getElementById('loginModal').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
    }

    /**
     * @function showLoading - 显示加载提示
     * @param {string} text - 加载文本
     */
    showLoading(text) {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingModal').classList.remove('hidden');
    }

    /**
     * @function updateLoadingText - 更新加载提示文本
     * @param {string} text - 新的加载文本
     */
    updateLoadingText(text) {
        document.getElementById('loadingText').textContent = text;
    }

    /**
     * @function hideLoading - 隐藏加载提示
     */
    hideLoading() {
        document.getElementById('loadingModal').classList.add('hidden');
    }

    /**
     * @function showError - 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        alert(`错误: ${message}`);
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    // 启动日志系统
    logger.info('系统', '应用开始初始化...');
    
    try {
        // 创建应用实例
        logger.debug('初始化', '创建应用实例');
        new OTAOrderApp();
        
        logger.success('系统', '应用初始化完成');
    } catch (error) {
        logger.error('初始化', '应用初始化失败', error);
    }
});